#include "CompressorControlsComponent.h"
#include "CustomLookAndFeel.h"

CompressorControlsComponent::CompressorControlsComponent(juce::AudioProcessorValueTreeState& apvts)
    : apvtsRef(apvts), // Initialize the reference
      thresholdKnob(apvts, "threshold", "THRESHOLD",
                    [](float v) { return juce::String(v, 1) + " dB"; }),
      ratioKnob(apvts, "ratio", "RATIO",
                [](float v) { return juce::String(v, 1) + ":1"; }),
      attackKnob(apvts, "attack", "ATTACK",
                 [](float v) { return juce::String(v, 0) + " ms"; }),
      releaseKnob(apvts, "release", "RELEASE",
                  [](float v) { return juce::String(v, 0) + " ms"; }),
      makeupKnob(apvts, "makeupGain", "MAKEUP",
                 [](float v) { return juce::String(v, 1) + " dB"; }),
      lowMidXKnob(apvts, "lowMidCrossover", "LOW-MID X",
                  [](float v) { return juce::String(int(v)) + " Hz"; }),
      midHighXKnob(apvts, "midHighCrossover", "MID-HIGH X",
                   [](float v) { return juce::String(int(v)) + " Hz"; }),
      multibandToggle(apvts, "multibandEnabled", "MULTIBAND")
{
    addAndMakeVisible(thresholdKnob);
    addAndMakeVisible(ratioKnob);
    addAndMakeVisible(attackKnob);
    addAndMakeVisible(releaseKnob);
    addAndMakeVisible(makeupKnob);
    addAndMakeVisible(lowMidXKnob);
    addAndMakeVisible(midHighXKnob);
    addAndMakeVisible(multibandToggle);
}

CompressorControlsComponent::~CompressorControlsComponent()
{
}

void CompressorControlsComponent::paint(juce::Graphics& g)
{
    auto bounds = getLocalBounds();

    // Draw glassmorphism panel
    if (auto* customLookAndFeel = dynamic_cast<CustomLookAndFeel*>(&getLookAndFeel()))
    {
        customLookAndFeel->drawGlassmorphismPanel(g, bounds, juce::Colour(0xff0284c7).withAlpha(0.7f));
    }
}

void CompressorControlsComponent::resized()
{
    auto bounds = getLocalBounds();
    bounds.reduce(16, 16); // Padding for glassmorphism panel

    // Define rows for layout
    juce::Array<juce::Component*> firstRowItems = { &thresholdKnob, &ratioKnob, &attackKnob };
    juce::Array<juce::Component*> secondRowItems = { &releaseKnob, &makeupKnob, &lowMidXKnob };
    juce::Array<juce::Component*> thirdRowItems = { &midHighXKnob, &multibandToggle };


    int numRows = 3;
    auto rowHeight = bounds.getHeight() / numRows;

    juce::Rectangle<int> rowBounds;

    // First row (3 items)
    rowBounds = bounds.removeFromTop(rowHeight);
    juce::Grid grid1;
    grid1.templateRows    = { juce::Grid::TrackInfo(juce::Grid::Fr(1)) };
    grid1.templateColumns = { juce::Grid::TrackInfo(juce::Grid::Fr(1)),
                              juce::Grid::TrackInfo(juce::Grid::Fr(1)),
                              juce::Grid::TrackInfo(juce::Grid::Fr(1)) };
    for (auto* item : firstRowItems) grid1.items.add(juce::GridItem(*item));
    grid1.performLayout(rowBounds.reduced(0, 4)); // Reduce vertical margin for row

    // Second row (3 items)
    rowBounds = bounds.removeFromTop(rowHeight);
    juce::Grid grid2;
    grid2.templateRows    = { juce::Grid::TrackInfo(juce::Grid::Fr(1)) };
    grid2.templateColumns = { juce::Grid::TrackInfo(juce::Grid::Fr(1)),
                              juce::Grid::TrackInfo(juce::Grid::Fr(1)),
                              juce::Grid::TrackInfo(juce::Grid::Fr(1)) };
    for (auto* item : secondRowItems) grid2.items.add(juce::GridItem(*item));
    grid2.performLayout(rowBounds.reduced(0, 4));

    // Third row (2 items) - needs different column setup
    rowBounds = bounds.removeFromTop(rowHeight);
    juce::Grid grid3;
    grid3.templateRows    = { juce::Grid::TrackInfo(juce::Grid::Fr(1)) };
    // For 2 items, could do Fr(1), Fr(1) and leave one empty, or Fr(0.666) Fr(0.333) etc.
    // Or just center them. For simplicity, let's use FlexBox for the last row for centering.
    juce::FlexBox flex3;
    flex3.justifyContent = juce::FlexBox::JustifyContent::spaceAround; // Or center
    flex3.alignItems = juce::FlexBox::AlignItems::center;
    flex3.items.add(juce::FlexItem(midHighXKnob).withFlex(1.0f).withMaxHeight(rowBounds.getHeight() * 0.9f)); // Max height to give some padding
    flex3.items.add(juce::FlexItem(multibandToggle).withFlex(1.0f).withMaxHeight(rowBounds.getHeight() * 0.9f));
    flex3.performLayout(rowBounds.reduced(0, 4));
}
