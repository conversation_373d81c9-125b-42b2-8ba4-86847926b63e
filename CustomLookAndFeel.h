#pragma once

#include <juce_gui_basics/juce_gui_basics.h>

class CustomLookAndFeel : public juce::LookAndFeel_V4
{
public:
    CustomLookAndFeel();
    ~CustomLookAndFeel() override;

    void drawRotarySlider (juce::Graphics& g, int x, int y, int width, int height,
                           float sliderPosProportional, float rotaryStartAngle, float rotaryEndAngle,
                           juce::Slider& slider) override;

    void drawComboBox (juce::Graphics& g, int width, int height, bool isButtonDown,
                       int buttonX, int buttonY, int buttonW, int buttonH,
                       juce::ComboBox& box) override;

    void drawLabel (juce::Graphics& g, juce::Label& label) override;

    void drawButtonBackground (juce::Graphics& g, juce::Button& button, const juce::Colour& backgroundColour,
                               bool shouldDrawButtonAsHighlighted, bool shouldDrawButtonAsDown) override;

    void drawToggleButton (juce::Graphics& g, juce::ToggleButton& button,
                           bool shouldDrawButtonAsHighlighted, bool shouldDrawButtonAsDown) override;

    // Glassmorphism panel drawing
    void drawGlassmorphismPanel(juce::Graphics& g, juce::Rectangle<int> bounds,
                               juce::Colour accentColor = juce::Colour(0xff06b6d4));

    // Enhanced level meter drawing
    void drawLevelMeter(juce::Graphics& g, juce::Rectangle<int> bounds,
                       float level, juce::Colour startColor, juce::Colour endColor);

    // Color constants for the theme
    static const juce::Colour primaryBackground;
    static const juce::Colour surfaceGlass;
    static const juce::Colour accentCyan;
    static const juce::Colour accentBlue;
    static const juce::Colour textPrimary;
    static const juce::Colour textSecondary;
    static const juce::Colour borderColor;

private:
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (CustomLookAndFeel)
};
