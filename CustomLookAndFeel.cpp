#include "CustomLookAndFeel.h"

// Color constants definition
const juce::Colour CustomLookAndFeel::primaryBackground = juce::Colour(0xff0f172a);
const juce::Colour CustomLookAndFeel::surfaceGlass = juce::Colour(0xb31e293b);
const juce::Colour CustomLookAndFeel::accentCyan = juce::Colour(0xff06b6d4);
const juce::Colour CustomLookAndFeel::accentBlue = juce::Colour(0xff0891b2);
const juce::Colour CustomLookAndFeel::textPrimary = juce::Colour(0xffe2e8f0);
const juce::Colour CustomLookAndFeel::textSecondary = juce::Colour(0xff94a3b8);
const juce::Colour CustomLookAndFeel::borderColor = juce::Colour(0xff475569);

CustomLookAndFeel::CustomLookAndFeel()
{
    // Set theme colors
    setColour(juce::Label::textColourId, textPrimary);
    setColour(juce::ComboBox::backgroundColourId, juce::Colour(0xFF1F2937));
    setColour(juce::ComboBox::textColourId, textPrimary);
    setColour(juce::Slider::thumbColourId, accentCyan);
    setColour(juce::Slider::rotarySliderFillColourId, juce::Colour(0xFF1F2937));
    setColour(juce::Slider::rotarySliderOutlineColourId, borderColor);
    setColour(juce::TextButton::textColourOffId, textPrimary);
    setColour(juce::TextButton::textColourOnId, juce::Colours::white);
}

CustomLookAndFeel::~CustomLookAndFeel()
{
}

void CustomLookAndFeel::drawRotarySlider (juce::Graphics& g, int x, int y, int width, int height,
                                       float sliderPosProportional, float rotaryStartAngle, float rotaryEndAngle,
                                       juce::Slider& slider)
{
    auto diameter = juce::jmin (width, height) - 10.0f; // Add some padding
    auto radius = diameter / 2.0f;
    auto centreX = x + width / 2.0f;
    auto centreY = y + height / 2.0f;
    auto rx = centreX - radius;
    auto ry = centreY - radius;
    auto rw = diameter;
    auto rh = diameter;
    auto angle = rotaryStartAngle + sliderPosProportional * (rotaryEndAngle - rotaryStartAngle);

    // Knob Body
    juce::ColourGradient knobGradient (juce::Colour(0xFF374151), rx, ry,
                                       juce::Colour(0xFF1F2937), rx + rw, ry + rh,
                                       true); // Diagonal gradient
    g.setGradientFill(knobGradient);
    g.fillEllipse (rx, ry, rw, rh);

    // Knob Border
    g.setColour (juce::Colour(0xFF4B5563));
    g.drawEllipse (rx, ry, rw, rh, 1.5f);

    // Pointer
    juce::Path pointerPath;
    auto pointerWidth = radius * 0.2f;
    auto pointerLength = radius * 0.9f;
    pointerPath.addRoundedRectangle(-pointerWidth / 2, -radius, pointerWidth, pointerLength, pointerWidth / 2);
    pointerPath.applyTransform (juce::AffineTransform::rotation (angle).translated (centreX, centreY));

    juce::ColourGradient pointerGradient (juce::Colour(0xFF06B6D4), 0, -radius,
                                          juce::Colour(0xFF0891B2), 0, -radius + pointerLength,
                                          false); // Vertical gradient for pointer
    g.setGradientFill(pointerGradient);
    g.fillPath(pointerPath);
}

void CustomLookAndFeel::drawComboBox (juce::Graphics& g, int width, int height, bool isButtonDown,
                                   int buttonX, int buttonY, int buttonW, int buttonH,
                                   juce::ComboBox& box)
{
    auto cornerSize = 5.0f; // Fixed corner size since getCornerSize() doesn't exist in this JUCE version

    // Background
    juce::ColourGradient bgGradient (juce::Colour(0xFF374151), 0.0f, 0.0f,
                                     juce::Colour(0xFF1F2937), (float)width, (float)height,
                                     true); // Diagonal gradient
    g.setGradientFill(bgGradient);
    // Use 0,0 for x,y as coordinates are relative to the ComboBox's bounds
    g.fillRoundedRectangle (0.0f, 0.0f, (float)width, (float)height, cornerSize);

    // Border
    float borderThickness = 1.5f;
    g.setColour (juce::Colour(0xFF4B5563));
    // Use 0,0 for x,y
    g.drawRoundedRectangle (0.0f, 0.0f, (float)width, (float)height, cornerSize, borderThickness);

    // Arrow
    juce::Path arrow;
    auto arrowX = (float)buttonX + (float)buttonW * 0.5f - 5.0f; // Center the arrow
    auto arrowY = (float)buttonY + (float)buttonH * 0.5f - 3.0f;
    arrow.addTriangle (arrowX, arrowY,
                       arrowX + 10.0f, arrowY,
                       arrowX + 5.0f, arrowY + 6.0f);
    g.setColour (juce::Colour(0xFFE2E8F0)); // Light grey for arrow
    g.fillPath (arrow);
}

void CustomLookAndFeel::drawLabel (juce::Graphics& g, juce::Label& label)
{
    g.fillAll (label.findColour (juce::Label::backgroundColourId));

    if (! label.isBeingEdited())
    {
        auto alpha = label.isEnabled() ? 1.0f : 0.5f;
        const juce::Font font (getLabelFont (label));

        g.setColour (label.findColour (juce::Label::textColourId).withMultipliedAlpha (alpha));
        g.setFont (font);

        auto textArea = getLabelBorderSize (label).subtractedFrom (label.getLocalBounds());

        g.drawText (label.getText(), textArea, label.getJustificationType(), true);

        g.setColour (label.findColour (juce::Label::outlineColourId).withMultipliedAlpha (alpha));
    }
    else if (label.isEnabled())
    {
        g.setColour (label.findColour (juce::Label::outlineColourId));
    }

    g.drawRect (label.getLocalBounds()); // Draw outline for debugging or styling
}

void CustomLookAndFeel::drawButtonBackground (juce::Graphics& g, juce::Button& button, const juce::Colour& backgroundColour,
                                           bool shouldDrawButtonAsHighlighted, bool shouldDrawButtonAsDown)
{
    auto bounds = button.getLocalBounds().toFloat();
    auto cornerSize = 6.0f; // Consistent corner size

    juce::ColourGradient gradient;
    juce::Colour borderColour;

    if (shouldDrawButtonAsDown || shouldDrawButtonAsHighlighted)
    {
        // Hover/Down state: linear-gradient(135deg, #0e7490 0%, #0891b2 100%)
        gradient = juce::ColourGradient(juce::Colour(0xFF0E7490), bounds.getBottomLeft(),
                                        juce::Colour(0xFF0891B2), bounds.getTopRight(),
                                        false); // diagonal
        borderColour = juce::Colour(0xFF07809E); // Slightly darker for pressed/hover
    }
    else
    {
        // Default state: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%)
        gradient = juce::ColourGradient(juce::Colour(0xFF0891B2), bounds.getBottomLeft(),
                                        juce::Colour(0xFF06B6D4), bounds.getTopRight(),
                                        false); // diagonal
        borderColour = juce::Colour(0xFF22D3EE);
    }

    g.setGradientFill(gradient);
    g.fillRoundedRectangle(bounds, cornerSize);

    g.setColour(borderColour);
    g.drawRoundedRectangle(bounds, cornerSize, 1.5f);

    // Note: Text color is typically handled by button.findColour(juce::TextButton::textColourOffId)
    // or by overriding drawButtonText if more control is needed.
}

void CustomLookAndFeel::drawToggleButton (juce::Graphics& g, juce::ToggleButton& button,
                                       bool shouldDrawButtonAsHighlighted, bool shouldDrawButtonAsDown)
{
    auto bounds = button.getLocalBounds().toFloat();
    auto cornerSize = bounds.getHeight() * 0.5f; // Make it a pill shape track
    float thumbWidthRatio = 0.4f; // Thumb takes 40% of the width
    float trackWidth = bounds.getWidth();
    float trackHeight = bounds.getHeight();

    juce::ColourGradient trackGradient;
    juce::Colour trackBorderColour;
    juce::Colour thumbColour = juce::Colour(0xFFE2E8F0); // Light grey thumb

    auto thumbBounds = juce::Rectangle<float>();
    float padding = trackHeight * 0.15f; // Padding for thumb inside track

    if (button.getToggleState()) // ON state
    {
        // ON state: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%)
        trackGradient = juce::ColourGradient(juce::Colour(0xFF0891B2), bounds.getBottomLeft(),
                                             juce::Colour(0xFF06B6D4), bounds.getTopRight(), false);
        trackBorderColour = juce::Colour(0xFF22D3EE);
        thumbBounds = juce::Rectangle<float>(trackWidth * (1.0f - thumbWidthRatio) - padding, padding,
                                             trackWidth * thumbWidthRatio - padding, trackHeight - 2 * padding);
    }
    else // OFF state
    {
        // OFF state: linear-gradient(135deg, #374151 0%, #1f2937 100%)
        trackGradient = juce::ColourGradient(juce::Colour(0xFF374151), bounds.getBottomLeft(),
                                             juce::Colour(0xFF1F2937), bounds.getTopRight(), false);
        trackBorderColour = juce::Colour(0xFF4B5563);
        thumbBounds = juce::Rectangle<float>(padding, padding,
                                             trackWidth * thumbWidthRatio - padding, trackHeight - 2 * padding);
    }

    // Draw Track
    g.setGradientFill(trackGradient);
    g.fillRoundedRectangle(bounds, cornerSize);
    g.setColour(trackBorderColour);
    g.drawRoundedRectangle(bounds, cornerSize, 1.5f);

    // Draw Thumb
    g.setColour(thumbColour);
    g.fillRoundedRectangle(thumbBounds, thumbBounds.getHeight() * 0.5f);

    // Power button specific styling would ideally be handled by a dedicated component
    // or by checking a component ID if this LookAndFeel is used for multiple toggle buttons.
    // For now, this is a generic toggle button.
}

void CustomLookAndFeel::drawGlassmorphismPanel(juce::Graphics& g, juce::Rectangle<int> bounds, juce::Colour accentColor)
{
    auto floatBounds = bounds.toFloat();

    // Background with transparency
    g.setColour(surfaceGlass);
    g.fillRoundedRectangle(floatBounds, 12.0f);

    // Border
    g.setColour(borderColor.withAlpha(0.8f));
    g.drawRoundedRectangle(floatBounds, 12.0f, 1.0f);

    // Accent line at top
    auto accentBounds = floatBounds.removeFromTop(4.0f).reduced(20.0f, 0.0f);
    juce::ColourGradient accentGradient(
        juce::Colours::transparentBlack, accentBounds.getX(), accentBounds.getCentreY(),
        accentColor, accentBounds.getCentreX(), accentBounds.getCentreY(),
        false
    );
    accentGradient.addColour(1.0, juce::Colours::transparentBlack);
    g.setGradientFill(accentGradient);
    g.fillRoundedRectangle(accentBounds, 2.0f);
}

void CustomLookAndFeel::drawLevelMeter(juce::Graphics& g, juce::Rectangle<int> bounds,
                                      float level, juce::Colour startColor, juce::Colour endColor)
{
    auto floatBounds = bounds.toFloat();

    // Background
    g.setColour(juce::Colour(0xff1e293b));
    g.fillRoundedRectangle(floatBounds, 8.0f);

    // Border
    g.setColour(juce::Colour(0xff374151));
    g.drawRoundedRectangle(floatBounds, 8.0f, 2.0f);

    // Level fill
    if (level > 0.0f)
    {
        auto fillBounds = floatBounds.reduced(2.0f);
        fillBounds.setWidth(fillBounds.getWidth() * juce::jlimit(0.0f, 1.0f, level));

        juce::ColourGradient levelGradient(startColor, fillBounds.getX(), fillBounds.getCentreY(),
                                          endColor, fillBounds.getRight(), fillBounds.getCentreY(),
                                          false);
        g.setGradientFill(levelGradient);
        g.fillRoundedRectangle(fillBounds, 6.0f);
    }

    // Grid lines
    g.setColour(juce::Colours::white.withAlpha(0.1f));
    for (int i = 1; i < 10; ++i)
    {
        float x = floatBounds.getX() + (floatBounds.getWidth() * i / 10.0f);
        g.drawVerticalLine(static_cast<int>(x), floatBounds.getY(), floatBounds.getBottom());
    }
}
