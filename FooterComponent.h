#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_audio_processors/juce_audio_processors.h>

class FooterComponent : public juce::Component
{
public:
    FooterComponent(juce::AudioProcessorValueTreeState& apvts);
    ~FooterComponent() override;

    void paint(juce::Graphics& g) override;
    void resized() override;

private:
    juce::AudioProcessorValueTreeState& apvtsRef;
    
    juce::TextButton bypassButton;
    juce::TextButton powerButton;
    juce::Label versionLabel;
    
    std::unique_ptr<juce::AudioProcessorValueTreeState::ButtonAttachment> bypassAttachment;
    
    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (FooterComponent)
};
