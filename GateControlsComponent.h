#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_audio_processors/juce_audio_processors.h>
#include "KnobWithLabels.h"
#include "CustomToggleButton.h"

class GateControlsComponent : public juce::Component
{
public:
    GateControlsComponent(juce::AudioProcessorValueTreeState& apvts);
    ~GateControlsComponent() override;

    void resized() override;

private:
    juce::AudioProcessorValueTreeState& apvtsRef;

    KnobWithLabels thresholdKnob;
    KnobWithLabels attackKnob;
    KnobWithLabels holdKnob;
    KnobWithLabels releaseKnob;
    CustomToggleButton enabledToggle;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (GateControlsComponent)
};
