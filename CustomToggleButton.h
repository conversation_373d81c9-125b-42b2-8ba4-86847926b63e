#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_audio_processors/juce_audio_processors.h>

class CustomToggleButton : public juce::Component
{
public:
    CustomToggleButton(juce::AudioProcessorValueTreeState& state,
                       const juce::String& parameterID,
                       const juce::String& labelText);
    ~CustomToggleButton() override;

    void resized() override;

    // Optional: provide access to the underlying button if needed
    // juce::ToggleButton& getButton() { return toggleButton; }

private:
    juce::ToggleButton toggleButton;
    juce::Label nameLabel;

    juce::AudioProcessorValueTreeState& apvts;
    juce::String paramID;
    std::unique_ptr<juce::AudioProcessorValueTreeState::ButtonAttachment> attachment;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (CustomToggleButton)
};
