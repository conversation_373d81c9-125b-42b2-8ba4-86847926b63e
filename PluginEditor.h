#pragma once

#include "PluginProcessor.h"
#include "CustomLookAndFeel.h"
#include "HeaderComponent.h"
#include "IoPresetComponent.h"
#include "ModuleSelectorComponent.h"
#include "CompressorControlsComponent.h"
#include "GateControlsComponent.h"       // Added
#include "AgcControlsComponent.h"        // Added
#include "DeesserControlsComponent.h"
#include "LimiterControlsComponent.h"
#include "LevelMonitoringComponent.h"    // Added
#include "FooterComponent.h"
#include "TabbedModuleSelectorComponent.h"

//==============================================================================
class AudioPluginAudioProcessorEditor final : public juce::AudioProcessorEditor,
                                              public juce::ComboBox::Listener // Added ComboBox::Listener
{
public:
    explicit AudioPluginAudioProcessorEditor (CompliAudioProcessor&);
    ~AudioPluginAudioProcessorEditor() override;

    //==============================================================================
    void paint (juce::Graphics&) override;
    void resized() override;

    void comboBoxChanged(juce::ComboBox* comboBoxThatHasChanged) override; // Added
    void switchToModule(int moduleIndex);

private:
    // This reference is provided as a quick way for your editor to
    // access the processor object that created it.
    CompliAudioProcessor& processorRef;

    HeaderComponent headerComponent;
    IoPresetComponent ioPresetComponent;
    TabbedModuleSelectorComponent tabbedModuleSelector;
    CompressorControlsComponent compressorControls;
    GateControlsComponent gateControls;             // Added
    AgcControlsComponent agcControls;
    DeesserControlsComponent deesserControls;
    LimiterControlsComponent limiterControls;
    LevelMonitoringComponent levelMonitoringComponent; // Changed type
    FooterComponent footerComponent;

    CustomLookAndFeel customLookAndFeel; // Added

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (AudioPluginAudioProcessorEditor)
};
