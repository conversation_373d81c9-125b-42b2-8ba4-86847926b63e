#pragma once

#include <juce_gui_basics/juce_gui_basics.h>

class ModuleSelectorComponent : public juce::Component,
                                public juce::ComboBox::Listener
{
public:
    ModuleSelectorComponent();
    ~ModuleSelectorComponent() override;

    void paint (juce::Graphics& g) override;
    void resized() override;

    void comboBoxChanged(juce::ComboBox* comboBoxThatHasChanged) override;

    juce::ComboBox& getModuleSelectorComboBox() { return moduleSelectorComboBox; }

private:
    juce::Label moduleLabel;
    juce::ComboBox moduleSelectorComboBox;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (ModuleSelectorComponent)
};
