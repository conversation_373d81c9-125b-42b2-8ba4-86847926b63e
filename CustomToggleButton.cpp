#include "CustomToggleButton.h"

CustomToggleButton::CustomToggleButton(juce::AudioProcessorValueTreeState& state,
                                       const juce::String& parameterID,
                                       const juce::String& labelText)
    : apvts(state),
      paramID(parameterID)
{
    // Name Label
    nameLabel.setText(labelText, juce::dontSendNotification);
    nameLabel.setFont(juce::Font(10.0f, juce::Font::plain)); // text-xs
    nameLabel.setColour(juce::Label::textColourId, juce::Colour(0xFF94a3b8)); // slate-400
    nameLabel.setJustificationType(juce::Justification::centred);
    addAndMakeVisible(nameLabel);

    // Toggle Button
    toggleButton.setButtonText(""); // LookAndFeel will draw the visual toggle
    // The LookAndFeel's drawToggleButton will handle the appearance
    addAndMakeVisible(toggleButton);

    // Attachment
    attachment = std::make_unique<juce::AudioProcessorValueTreeState::ButtonAttachment>(apvts, paramID, toggleButton);
}

CustomToggleButton::~CustomToggleButton()
{
}

void CustomToggleButton::resized()
{
    auto bounds = getLocalBounds();
    int labelHeight = 15;
    int toggleButtonHeight = 25; // Or derive from bounds and aspect ratio
    int spacing = 5;

    nameLabel.setBounds(bounds.removeFromTop(labelHeight));
    // Center the toggle button horizontally, below the label
    int toggleButtonWidth = toggleButtonHeight * 2; // Assuming a typical 2:1 aspect ratio for toggle switches
    if (toggleButton.getLookAndFeel().isColourSpecified(juce::ToggleButton::ColourIds::tickDisabledColourId)) { // Check if L&F provides specific toggle sizes
        // Potentially adjust toggleButtonWidth based on LookAndFeel metrics if available
    }

    toggleButton.setBounds(
        (bounds.getWidth() - toggleButtonWidth) / 2,
        nameLabel.getBottom() + spacing,
        toggleButtonWidth,
        toggleButtonHeight
    );
    // Or let it take a significant portion of the width if the L&F handles it well:
    // toggleButton.setBounds(bounds.reduced(bounds.getWidth() * 0.2f, 0).removeFromTop(toggleButtonHeight));
}
