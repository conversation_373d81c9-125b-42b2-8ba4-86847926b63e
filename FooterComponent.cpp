#include "FooterComponent.h"
#include "CustomLookAndFeel.h"

FooterComponent::FooterComponent(juce::AudioProcessorValueTreeState& apvts)
    : apvtsRef(apvts)
{
    // Bypass button
    bypassButton.setButtonText("BYPASS");
    bypassButton.setColour(juce::TextButton::buttonColourId, CustomLookAndFeel::accentCyan);
    addAndMakeVisible(bypassButton);
    
    // Power button
    powerButton.setButtonText("●");
    powerButton.setColour(juce::TextButton::buttonColourId, juce::Colour(0xff10b981));
    powerButton.setColour(juce::TextButton::buttonOnColourId, juce::Colour(0xffdc2626));
    powerButton.setToggleable(true);
    powerButton.setToggleState(true, juce::dontSendNotification);
    addAndMakeVisible(powerButton);
    
    // Version label
    versionLabel.setText("v2.1.4", juce::dontSendNotification);
    versionLabel.setColour(juce::Label::textColourId, CustomLookAndFeel::textSecondary);
    versionLabel.setJustificationType(juce::Justification::centred);
    versionLabel.setFont(juce::Font(12.0f, juce::Font::bold));
    addAndMakeVisible(versionLabel);
    
    // Attach bypass to parameter if it exists
    if (apvts.getParameter("bypass") != nullptr)
    {
        bypassAttachment = std::make_unique<juce::AudioProcessorValueTreeState::ButtonAttachment>(
            apvts, "bypass", bypassButton);
    }
}

FooterComponent::~FooterComponent()
{
}

void FooterComponent::paint(juce::Graphics& g)
{
    auto bounds = getLocalBounds();
    
    // Border line at top
    g.setColour(CustomLookAndFeel::borderColor.withAlpha(0.5f));
    g.drawHorizontalLine(0, 0.0f, static_cast<float>(getWidth()));
}

void FooterComponent::resized()
{
    auto bounds = getLocalBounds();
    bounds.removeFromTop(1); // Account for border line
    
    auto leftSection = bounds.removeFromLeft(bounds.getWidth() * 0.6f);
    auto rightSection = bounds;
    
    // Left section: Bypass and Power buttons
    auto buttonArea = leftSection.reduced(12, 8);
    
    bypassButton.setBounds(buttonArea.removeFromLeft(100));
    buttonArea.removeFromLeft(16); // Spacing
    
    // Power button area
    auto powerArea = buttonArea.removeFromLeft(80);
    auto powerLabel = powerArea.removeFromLeft(40);
    auto powerBtn = powerArea;
    
    // Add "POWER" label (we could create a separate label component)
    powerButton.setBounds(powerBtn.withSizeKeepingCentre(48, 48));
    
    // Right section: Version
    versionLabel.setBounds(rightSection.reduced(12, 8));
}
