#include "DeesserControlsComponent.h"

DeesserControlsComponent::DeesserControlsComponent(juce::AudioProcessorValueTreeState& apvts)
    : apvtsRef(apvts),
      frequencyKnob(apvts, "deEsserFreq", "FREQUENCY",
                    [](float v) { return juce::String(int(v)) + " Hz"; }),
      thresholdKnob(apvts, "deEsserThreshold", "THRESHOLD",
                    [](float v) { return juce::String(v, 1) + " dB"; }),
      reductionKnob(apvts, "deEsserReduction", "REDUCTION", // Parameter ID might be different, e.g., "deEsserRange" or "deEsserStrength"
                    [](float v) { return juce::String(v, 1) + " dB"; }),
      enabledToggle(apvts, "deEsserEnabled", "ENABLED")
{
    addAndMakeVisible(frequencyKnob);
    addAndMakeVisible(thresholdKnob);
    addAndMakeVisible(reductionKnob);
    addAndMakeVisible(enabledToggle);
}

DeesserControlsComponent::~DeesserControlsComponent()
{
}

void DeesserControlsComponent::resized()
{
    auto bounds = getLocalBounds();
    int margin = 10;
    bounds.reduce(margin, margin);

    juce::Array<juce::Component*> firstRowItems = { &frequencyKnob, &thresholdKnob, &reductionKnob };

    int numRows = 2;
    auto rowHeight = bounds.getHeight() / numRows;

    juce::Rectangle<int> rowBounds;

    // First row (3 knobs)
    rowBounds = bounds.removeFromTop(rowHeight);
    juce::Grid grid1;
    grid1.templateRows    = { juce::Grid::TrackInfo(juce::Grid::Fr(1)) };
    grid1.templateColumns = { juce::Grid::TrackInfo(juce::Grid::Fr(1)),
                              juce::Grid::TrackInfo(juce::Grid::Fr(1)),
                              juce::Grid::TrackInfo(juce::Grid::Fr(1)) };
    for (auto* item : firstRowItems) grid1.items.add(juce::GridItem(*item));
    grid1.performLayout(rowBounds.reduced(0, margin / 2));

    // Second row (1 toggle, centered)
    rowBounds = bounds.removeFromTop(rowHeight);
    juce::FlexBox flex2;
    flex2.justifyContent = juce::FlexBox::JustifyContent::center;
    flex2.alignItems = juce::FlexBox::AlignItems::center;
    flex2.items.add(juce::FlexItem(enabledToggle).withFlex(0).withMinWidth(100).withMaxHeight(rowBounds.getHeight() * 0.5f));
    flex2.performLayout(rowBounds.reduced(0, margin/2));
}
