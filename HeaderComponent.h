#pragma once

#include <juce_gui_basics/juce_gui_basics.h>

class HeaderComponent : public juce::Component
{
public:
    HeaderComponent();
    ~HeaderComponent() override;

    void paint (juce::Graphics& g) override;
    void resized() override;

private:
    juce::Label titleLabel;
    juce::Label subtitleLabel;

    // Store position for the decorative line if calculated in resized()
    // float lineYPosition;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (HeaderComponent)
};
