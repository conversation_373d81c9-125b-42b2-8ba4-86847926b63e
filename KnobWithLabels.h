#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_audio_processors/juce_audio_processors.h>
#include <functional> // For std::function

class KnobWithLabels : public juce::Component,
                       public juce::Slider::Listener
{
public:
    KnobWithLabels(juce::AudioProcessorValueTreeState& state,
                   const juce::String& parameterID,
                   const juce::String& labelText,
                   std::function<juce::String(float)> formatter = nullptr);
    ~KnobWithLabels() override;

    void resized() override;
    void sliderValueChanged(juce::Slider* slider) override;

private:
    void updateValueLabel();

    juce::Slider knob;
    juce::Label nameLabel;
    juce::Label valueLabel;

    juce::AudioProcessorValueTreeState& apvts;
    juce::String paramID;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> attachment;

    std::function<juce::String(float)> valueToString;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (KnobWithLabels)
};
