#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_audio_processors/juce_audio_processors.h>
#include "KnobWithLabels.h"
#include "CustomToggleButton.h"

class CompressorControlsComponent : public juce::Component
{
public:
    CompressorControlsComponent(juce::AudioProcessorValueTreeState& apvts);
    ~CompressorControlsComponent() override;

    void paint(juce::Graphics& g) override;
    void resized() override;

private:
    juce::AudioProcessorValueTreeState& apvtsRef; // Store reference if needed beyond constructor

    KnobWithLabels thresholdKnob;
    KnobWithLabels ratioKnob;
    KnobWithLabels attackKnob;
    KnobWithLabels releaseKnob;
    KnobWithLabels makeupKnob;
    KnobWithLabels lowMidXKnob;    // For "LOW-MID X"
    KnobWithLabels midHighXKnob;   // For "MID-HIGH X"
    CustomToggleButton multibandToggle;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (CompressorControlsComponent)
};
