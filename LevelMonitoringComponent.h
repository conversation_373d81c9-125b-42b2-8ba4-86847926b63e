#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_core/juce_core.h> // For std::atomic
#include "LevelMeterComponent.h"
#include "PluginProcessor.h" // For processorRef

class LevelMonitoringComponent : public juce::Component, public juce::Timer
{
public:
    LevelMonitoringComponent(CompliAudioProcessor& proc);
    ~LevelMonitoringComponent() override;

    void paint (juce::Graphics& g) override;
    void resized() override;
    void timerCallback() override;

private:
    void updateValueLabels();
    void layoutMeterSection(juce::Rectangle<int> bounds,
                           juce::Label& label,
                           LevelMeterComponent& meter,
                           juce::Label& valueLabel,
                           int labelHeight, int meterHeight, int valueLabelHeight);

    CompliAudioProcessor& processorRef;

    LevelMeterComponent inputMeter;
    LevelMeterComponent outputMeter;
    LevelMeterComponent grMeter;

    juce::Label inputLabel;
    juce::Label outputLabel;
    juce::Label grLabel;

    juce::Label inputValueLabel;
    juce::Label outputValueLabel;
    juce::Label grValueLabel;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (LevelMonitoringComponent)
};
