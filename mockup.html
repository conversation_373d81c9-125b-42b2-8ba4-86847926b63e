<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Compli Audio Processor - Enhanced</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      ::-webkit-scrollbar-track {
        background: rgba(15, 23, 42, 0.5);
        border-radius: 3px;
      }
      ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #0891b2, #06b6d4);
        border-radius: 3px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #0e7490, #0891b2);
      }
      body {
        font-family: "Inter", sans-serif;
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        min-height: 100vh;
      }
      .knob {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        position: relative;
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        border: 3px solid #4b5563;
        box-shadow: 0 0 20px rgba(6, 182, 212, 0.2),
          inset 0 2px 4px rgba(255, 255, 255, 0.1),
          inset 0 -2px 4px rgba(0, 0, 0, 0.3);
        cursor: pointer;
        transition: all 0.2s ease;
      }
      .knob:hover {
        border-color: #06b6d4;
        box-shadow: 0 0 25px rgba(6, 182, 212, 0.4),
          inset 0 2px 4px rgba(255, 255, 255, 0.1),
          inset 0 -2px 4px rgba(0, 0, 0, 0.3);
        transform: scale(1.05);
      }
      .knob-pointer {
        width: 3px;
        height: 25px;
        background: linear-gradient(180deg, #06b6d4 0%, #0891b2 100%);
        position: absolute;
        left: calc(50% - 1.5px);
        top: 8px;
        border-radius: 2px;
        transform-origin: bottom center;
        box-shadow: 0 0 8px rgba(6, 182, 212, 0.6);
      }
      .level-meter {
        width: 100%;
        height: 16px;
        background: linear-gradient(90deg, #1e293b 0%, #334155 100%);
        border-radius: 8px;
        overflow: hidden;
        border: 2px solid #374151;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
        position: relative;
      }
      .level-meter::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(
          90deg,
          transparent 0,
          transparent 8px,
          rgba(255, 255, 255, 0.1) 8px,
          rgba(255, 255, 255, 0.1) 9px
        );
        pointer-events: none;
      }
      .level-meter-fill {
        height: 100%;
        border-radius: 6px;
        transition: width 0.1s linear;
        position: relative;
      }
      .level-meter-fill.input-meter {
        background: linear-gradient(
          90deg,
          #10b981 0%,
          #22c55e 50%,
          #eab308 80%,
          #dc2626 100%
        );
      }
      .level-meter-fill.output-meter {
        background: linear-gradient(
          90deg,
          #06b6d4 0%,
          #3b82f6 50%,
          #8b5cf6 100%
        );
      }
      .level-meter-fill.gr-meter {
        background: linear-gradient(90deg, #f59e0b 0%, #ef4444 100%);
      }
      .compli-surface-glass {
        background: rgba(30, 41, 59, 0.7);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(71, 85, 105, 0.8);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }
      .section-accent-line {
        height: 4px;
        border-radius: 2px;
        background: linear-gradient(
          90deg,
          transparent 0%,
          var(--accent-color) 10%,
          var(--accent-color) 90%,
          transparent 100%
        );
        box-shadow: 0 0 10px var(--accent-color);
      }
      .toggle-button {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        border: 2px solid #4b5563;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }
      .toggle-button::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(6, 182, 212, 0.3),
          transparent
        );
        transition: left 0.5s ease;
      }
      .toggle-button:hover::before {
        left: 100%;
      }
      .toggle-button span {
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
      }
      .toggle-button[data-state="on"] {
        background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
        border-color: #22d3ee;
        box-shadow: 0 0 15px rgba(6, 182, 212, 0.5),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }
      .btn-primary {
        background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
        border: 2px solid #22d3ee;
        box-shadow: 0 4px 6px rgba(6, 182, 212, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transition: all 0.2s ease;
      }
      .btn-primary:hover {
        background: linear-gradient(135deg, #0e7490 0%, #0891b2 100%);
        transform: translateY(-1px);
        box-shadow: 0 6px 8px rgba(6, 182, 212, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }
      .btn-secondary {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        border: 2px solid #4b5563;
        transition: all 0.2s ease;
      }
      .btn-secondary:hover {
        background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
        border-color: #6b7280;
        transform: translateY(-1px);
      }
      .power-button {
        background: radial-gradient(circle, #374151 0%, #1f2937 100%);
        border: 3px solid #4b5563;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3),
          inset 0 2px 4px rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
      }
      .power-button[data-state="on"] {
        background: radial-gradient(circle, #10b981 0%, #059669 100%);
        border-color: #22c55e;
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.6),
          0 4px 8px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(255, 255, 255, 0.2);
        color: #fff;
      }
      .power-button[data-state="off"] {
        background: radial-gradient(circle, #dc2626 0%, #991b1b 100%);
        border-color: #ef4444;
        box-shadow: 0 0 20px rgba(220, 38, 38, 0.6),
          0 4px 8px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(255, 255, 255, 0.1);
        color: #fff;
      }
      .form-select {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        border: 2px solid #4b5563;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        transition: all 0.2s ease;
      }
      .form-select:focus {
        border-color: #06b6d4;
        box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.2),
          inset 0 2px 4px rgba(0, 0, 0, 0.2);
        outline: none;
      }
      .control-group {
        display: none;
        gap: 1.5rem;
      }
      .control-group.active {
        display: flex;
        flex-direction: column;
      }
      .control-row {
        display: grid;
        gap: 1.5rem;
        align-items: start;
        justify-items: center;
      }
      .control-knob-area {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        min-width: 90px;
      }
      .control-toggle-area {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
      }
      @keyframes pulse-glow {
        0%,
        100% {
          box-shadow: 0 0 15px rgba(6, 182, 212, 0.5);
        }
        50% {
          box-shadow: 0 0 25px rgba(6, 182, 212, 0.8);
        }
      }
      .animate-pulse-glow {
        animation: pulse-glow 2s ease-in-out infinite;
      }
      @media (max-width: 768px) {
        .knob {
          width: 60px;
          height: 60px;
        }
        .knob-pointer {
          height: 20px;
          top: 6px;
        }
      }
    </style>
  </head>
  <body class="flex items-center justify-center min-h-screen p-2 sm:p-4">
    <div
      id="pluginEditor"
      class="w-full max-w-[950px] min-h-[680px] max-h-[90vh] p-4 rounded-2xl shadow-2xl flex flex-col gap-4 overflow-hidden compli-surface-glass"
    >
      <header class="text-center py-3 relative">
        <div
          class="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-500/10 to-transparent rounded-lg"
        ></div>
        <h1
          class="text-4xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent"
        >
          compli
        </h1>
        <p class="text-xs text-slate-400 tracking-[.2em] font-medium mt-1">
          AUDIO PROCESSOR
        </p>
        <div
          class="w-24 h-1 bg-gradient-to-r from-cyan-500 to-blue-500 mx-auto mt-2 rounded-full"
        ></div>
      </header>
      <div class="flex-grow flex flex-col gap-4 overflow-y-auto pr-1 pb-1">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <section class="p-4 rounded-xl compli-surface-glass relative">
            <div
              class="section-accent-line mb-3"
              style="--accent-color: #06b6d4"
            ></div>
            <h3 class="text-sm font-semibold text-cyan-400 mb-3 tracking-wider">
              AUDIO I/O
            </h3>
            <div class="space-y-3">
              <div>
                <label
                  for="inputDevice"
                  class="block text-xs font-medium text-slate-300 mb-1.5"
                  >INPUT DEVICE</label
                >
                <div class="flex items-center space-x-2">
                  <select
                    id="inputDevice"
                    class="flex-1 p-2.5 form-select rounded-lg text-sm text-slate-100 focus:ring-cyan-500 focus:border-cyan-500"
                  >
                    <option>Default Input Device</option>
                    <option>Microphone Array (Realtek)</option></select
                  ><button
                    class="p-2.5 btn-secondary rounded-lg text-slate-300 text-sm flex-shrink-0 hover:text-cyan-400"
                  >
                    <svg
                      class="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      ></path>
                    </svg>
                  </button>
                </div>
              </div>
              <div>
                <label
                  for="outputDevice"
                  class="block text-xs font-medium text-slate-300 mb-1.5"
                  >OUTPUT DEVICE</label
                >
                <div class="flex items-center space-x-2">
                  <select
                    id="outputDevice"
                    class="flex-1 p-2.5 form-select rounded-lg text-sm text-slate-100 focus:ring-cyan-500 focus:border-cyan-500"
                  >
                    <option>Default Output Device</option>
                    <option>Speakers (Realtek)</option></select
                  ><button
                    class="p-2.5 btn-secondary rounded-lg text-slate-300 text-sm flex-shrink-0 hover:text-cyan-400"
                  >
                    <svg
                      class="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      ></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </section>
          <section class="p-4 rounded-xl compli-surface-glass relative">
            <div
              class="section-accent-line mb-3"
              style="--accent-color: #0891b2"
            ></div>
            <h3 class="text-sm font-semibold text-blue-400 mb-3 tracking-wider">
              PRESETS
            </h3>
            <div>
              <label
                for="presetSelector"
                class="block text-xs font-medium text-slate-300 mb-1.5"
                >PRESET</label
              ><select
                id="presetSelector"
                class="w-full p-2.5 form-select rounded-lg text-sm text-slate-100 focus:ring-cyan-500 focus:border-cyan-500"
              >
                <option value="1">Voice Optimized</option>
                <option value="2">Broadcast</option>
                <option value="3">Podcast</option>
                <option value="4">Gaming</option>
                <option value="5">Custom</option>
              </select>
              <p
                id="presetInfoLabel"
                class="text-xs text-cyan-400 mt-2 text-center font-medium"
              >
                Optimal voice settings applied
              </p>
            </div>
          </section>
        </div>
        <section class="p-4 rounded-xl compli-surface-glass">
          <div
            class="section-accent-line mb-3"
            style="--accent-color: #0284c7"
          ></div>
          <div class="flex items-center gap-4">
            <h3 class="text-sm font-semibold text-sky-400 tracking-wider">
              MODULE
            </h3>
            <select
              id="moduleSelector"
              class="flex-grow p-2.5 form-select rounded-lg text-sm text-slate-100 focus:ring-cyan-500 focus:border-cyan-500"
            >
              <option value="compressor">Compressor</option>
              <option value="gate">Noise Gate</option>
              <option value="agc">AGC</option>
              <option value="deesser">De-esser</option>
              <option value="limiter">Limiter</option>
            </select>
          </div>
        </section>
        <section
          id="controlsArea"
          class="p-4 rounded-xl compli-surface-glass min-h-[320px] flex-grow"
        >
          <div
            class="section-accent-line mb-4"
            style="--accent-color: rgba(2, 132, 199, 0.7)"
          ></div>
          <div id="compressorControls" class="control-group active">
            <div class="control-row grid-cols-3">
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >THRESHOLD</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(120deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400"
                  >-18.0 dB</span
                >
              </div>
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >RATIO</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(90deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">3.0:1</span>
              </div>
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >ATTACK</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(45deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">8.0 ms</span>
              </div>
            </div>
            <div class="control-row grid-cols-3">
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >RELEASE</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(150deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">55.0 ms</span>
              </div>
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >MAKEUP</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(70deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">+2.5 dB</span>
              </div>
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >LOW-MID X</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(60deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">250 Hz</span>
              </div>
            </div>
            <div class="control-row grid-cols-2 items-center">
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >MID-HIGH X</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(180deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">2500 Hz</span>
              </div>
              <div class="control-toggle-area">
                <label
                  class="text-xs font-medium text-slate-300 tracking-wider mb-2"
                  >MULTIBAND</label
                ><button
                  class="toggle-button w-14 h-7 rounded-full p-1 flex items-center"
                  data-state="off"
                >
                  <span
                    class="w-5 h-5 rounded-full shadow-md transform transition-transform duration-300"
                  ></span>
                </button>
              </div>
            </div>
          </div>
          <div id="gateControls" class="control-group">
            <div class="control-row grid-cols-3">
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >THRESHOLD</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(-30deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400"
                  >-40.0 dB</span
                >
              </div>
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >ATTACK</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(30deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">5.0 ms</span>
              </div>
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >HOLD</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(100deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">100 ms</span>
              </div>
            </div>
            <div class="control-row grid-cols-2 items-center">
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >RELEASE</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(180deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">200 ms</span>
              </div>
              <div class="control-toggle-area">
                <label
                  class="text-xs font-medium text-slate-300 tracking-wider mb-2"
                  >ENABLED</label
                ><button
                  class="toggle-button w-14 h-7 rounded-full p-1 flex items-center"
                  data-state="on"
                >
                  <span
                    class="w-5 h-5 rounded-full shadow-md transform transition-transform duration-300 translate-x-7"
                  ></span>
                </button>
              </div>
            </div>
          </div>
          <div id="agcControls" class="control-group">
            <div class="control-row grid-cols-2">
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >TARGET LVL</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(120deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400"
                  >-18.0 dB</span
                >
              </div>
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >RESPONSE</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(200deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">1000 ms</span>
              </div>
            </div>
            <div class="control-row grid-cols-1">
              <div class="control-toggle-area">
                <label
                  class="text-xs font-medium text-slate-300 tracking-wider mb-2"
                  >ENABLED</label
                ><button
                  class="toggle-button w-14 h-7 rounded-full p-1 flex items-center"
                  data-state="on"
                >
                  <span
                    class="w-5 h-5 rounded-full shadow-md transform transition-transform duration-300 translate-x-7"
                  ></span>
                </button>
              </div>
            </div>
          </div>
          <div id="deesserControls" class="control-group">
            <div class="control-row grid-cols-3">
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >FREQUENCY</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(150deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">6000 Hz</span>
              </div>
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >THRESHOLD</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(100deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400"
                  >-12.0 dB</span
                >
              </div>
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >REDUCTION</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(60deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">6.0 dB</span>
              </div>
            </div>
            <div class="control-row grid-cols-1">
              <div class="control-toggle-area">
                <label
                  class="text-xs font-medium text-slate-300 tracking-wider mb-2"
                  >ENABLED</label
                ><button
                  class="toggle-button w-14 h-7 rounded-full p-1 flex items-center"
                  data-state="on"
                >
                  <span
                    class="w-5 h-5 rounded-full shadow-md transform transition-transform duration-300 translate-x-7"
                  ></span>
                </button>
              </div>
            </div>
          </div>
          <div id="limiterControls" class="control-group">
            <div class="control-row grid-cols-3">
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >THRESHOLD</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(160deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">-3.0 dB</span>
              </div>
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >ATTACK</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(20deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">1.0 ms</span>
              </div>
              <div class="control-knob-area">
                <label class="text-xs font-medium text-slate-300 tracking-wider"
                  >RELEASE</label
                >
                <div class="knob">
                  <div
                    class="knob-pointer"
                    style="transform: rotate(140deg)"
                  ></div>
                </div>
                <span class="text-xs font-semibold text-cyan-400">50 ms</span>
              </div>
            </div>
            <div class="control-row grid-cols-1">
              <div class="control-toggle-area">
                <label
                  class="text-xs font-medium text-slate-300 tracking-wider mb-2"
                  >ENABLED</label
                ><button
                  class="toggle-button w-14 h-7 rounded-full p-1 flex items-center"
                  data-state="on"
                >
                  <span
                    class="w-5 h-5 rounded-full shadow-md transform transition-transform duration-300 translate-x-7"
                  ></span>
                </button>
              </div>
            </div>
          </div>
        </section>
        <section class="p-4 rounded-xl compli-surface-glass">
          <div
            class="section-accent-line mb-3"
            style="--accent-color: #10b981"
          ></div>
          <h3
            class="text-sm font-semibold text-emerald-400 mb-3 tracking-wider text-center"
          >
            LEVEL MONITORING
          </h3>
          <div class="grid grid-cols-3 gap-4 items-center">
            <div class="text-center">
              <label
                class="block text-xs font-medium text-slate-300 mb-2 tracking-wider"
                >INPUT</label
              >
              <div class="level-meter mx-auto max-w-[100px]">
                <div
                  class="level-meter-fill input-meter"
                  style="width: 70%"
                ></div>
              </div>
              <span class="block text-xs font-semibold text-cyan-400 mt-2"
                >-8 dB</span
              >
            </div>
            <div class="text-center">
              <label
                class="block text-xs font-medium text-slate-300 mb-2 tracking-wider"
                >OUTPUT</label
              >
              <div class="level-meter mx-auto max-w-[100px]">
                <div
                  class="level-meter-fill output-meter"
                  style="width: 85%"
                ></div>
              </div>
              <span class="block text-xs font-semibold text-cyan-400 mt-2"
                >-3 dB</span
              >
            </div>
            <div class="text-center">
              <label
                class="block text-xs font-medium text-slate-300 mb-2 tracking-wider"
                >GR</label
              >
              <div class="level-meter mx-auto max-w-[100px]">
                <div class="level-meter-fill gr-meter" style="width: 20%"></div>
              </div>
              <span class="block text-xs font-semibold text-amber-400 mt-2"
                >-4 dB</span
              >
            </div>
          </div>
        </section>
      </div>
      <footer
        class="flex items-center justify-between p-3 border-t border-slate-600/50 mt-auto flex-shrink-0"
      >
        <div class="flex items-center gap-4">
          <button
            class="px-6 py-2.5 text-sm font-semibold text-white btn-primary rounded-lg transition-all duration-200"
          >
            BYPASS
          </button>
          <div class="flex items-center gap-2">
            <span class="text-xs font-medium text-slate-400 tracking-wider"
              >POWER</span
            ><button
              id="powerButton"
              class="power-button w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold transition-all duration-300"
              data-state="on"
            >
              ●
            </button>
          </div>
        </div>
        <div class="text-xs text-slate-500 font-medium">v2.1.4</div>
      </footer>
    </div>
    <script>
      const moduleSelector = document.getElementById("moduleSelector"),
        controlGroups = {
          compressor: document.getElementById("compressorControls"),
          gate: document.getElementById("gateControls"),
          agc: document.getElementById("agcControls"),
          deesser: document.getElementById("deesserControls"),
          limiter: document.getElementById("limiterControls"),
        };
      moduleSelector.addEventListener("change", function () {
        Object.values(controlGroups).forEach((g) => {
          if (g) g.classList.remove("active");
        });
        const sG = controlGroups[this.value];
        if (sG) sG.classList.add("active");
      });
      const presetSelector = document.getElementById("presetSelector"),
        presetInfoLabel = document.getElementById("presetInfoLabel"),
        presetInfoTexts = {
          1: "Optimal voice settings applied",
          2: "Professional broadcast settings",
          3: "Podcast optimized (-16 LUFS target)",
          4: "Gaming: Fast response",
          5: "User-defined settings",
        };
      presetSelector.addEventListener("change", function () {
        presetInfoLabel.textContent =
          presetInfoTexts[this.value] || "Select a preset";
      });
      document.querySelectorAll(".toggle-button").forEach((b) => {
        const t = b.querySelector("span"),
          isPowerButton = b.id === "powerButton";
        function updateToggleState(btn, state) {
          const thumb = btn.querySelector("span");
          btn.dataset.state = state;
          if (state === "on") {
            if (thumb) thumb.style.transform = "translateX(1.75rem)";
            btn.classList.remove(
              "bg-gradient-to-r",
              "from-gray-600",
              "to-gray-700"
            );
            btn.classList.add(
              "bg-gradient-to-r",
              "from-cyan-500",
              "to-blue-500"
            );
            if (isPowerButton) {
              btn.classList.remove("text-red-500");
              btn.classList.add("text-green-400");
            }
          } else {
            if (thumb) thumb.style.transform = "translateX(0px)";
            btn.classList.remove(
              "bg-gradient-to-r",
              "from-cyan-500",
              "to-blue-500"
            );
            btn.classList.add(
              "bg-gradient-to-r",
              "from-gray-600",
              "to-gray-700"
            );
            if (isPowerButton) {
              btn.classList.remove("text-green-400");
              btn.classList.add("text-red-500");
            }
          }
        }
        updateToggleState(b, b.dataset.state);
        b.addEventListener("click", function () {
          const nS = this.dataset.state === "on" ? "off" : "on";
          updateToggleState(this, nS);
        });
      });
      document.querySelectorAll(".knob").forEach((k) => {
        k.addEventListener("mouseenter", function () {
          this.classList.add("animate-pulse-glow");
        });
        k.addEventListener("mouseleave", function () {
          this.classList.remove("animate-pulse-glow");
        });
      });
      function animateLevelMeters() {
        const m = document.querySelectorAll(".level-meter-fill");
        m.forEach((e) => {
          const t = parseInt(e.style.width),
            a = Math.random() * 10 - 5,
            n = Math.max(0, Math.min(100, t + a));
          e.style.width = n + "%";
        });
      }
      setInterval(animateLevelMeters, 100);
    </script>
  </body>
</html>
