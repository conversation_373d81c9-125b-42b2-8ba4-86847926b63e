{"artifacts": [{"path": "juce_vst3_helper.exe"}, {"path": "juce_vst3_helper.pdb"}], "backtrace": 7, "backtraceGraph": {"commands": ["add_executable", "_juce_add_vst3_manifest_helper_target", "juce_enable_vst3_manifest_step", "_juce_set_plugin_target_properties", "_juce_link_plugin_wrapper", "_juce_configure_plugin_targets", "juce_add_plugin", "target_link_libraries", "target_include_directories", "target_compile_features"], "files": ["JUCE/extras/Build/CMake/JUCEUtils.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 6, "file": 1, "line": 40, "parent": 0}, {"command": 5, "file": 0, "line": 2182, "parent": 1}, {"command": 4, "file": 0, "line": 1589, "parent": 2}, {"command": 3, "file": 0, "line": 1452, "parent": 3}, {"command": 2, "file": 0, "line": 1234, "parent": 4}, {"command": 1, "file": 0, "line": 1098, "parent": 5}, {"command": 0, "file": 0, "line": 1040, "parent": 6}, {"command": 7, "file": 0, "line": 1058, "parent": 6}, {"command": 7, "file": 0, "line": 1047, "parent": 6}, {"command": 8, "file": 0, "line": 1043, "parent": 6}, {"command": 9, "file": 0, "line": 1048, "parent": 6}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 8, "fragment": "/Od"}, {"backtrace": 8, "fragment": "/MP"}, {"backtrace": 8, "fragment": "/EHsc"}, {"backtrace": 8, "fragment": "/Z7"}], "defines": [{"backtrace": 9, "define": "DEBUG=1"}, {"backtrace": 9, "define": "JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1"}, {"backtrace": 9, "define": "_DEBUG=1"}], "includes": [{"backtrace": 10, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/VST3_SDK"}, {"backtrace": 10, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules"}], "language": "CXX", "languageStandard": {"backtraces": [11], "standard": "17"}, "sourceIndexes": [0]}], "id": "juce_vst3_helper::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "juce_vst3_helper", "nameOnDisk": "juce_vst3_helper.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 7, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_plugin_client/VST3/juce_VST3ManifestHelper.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}