#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_audio_processors/juce_audio_processors.h>
#include "KnobWithLabels.h"
#include "CustomToggleButton.h"

class AgcControlsComponent : public juce::Component
{
public:
    AgcControlsComponent(juce::AudioProcessorValueTreeState& apvts);
    ~AgcControlsComponent() override;

    void resized() override;

private:
    juce::AudioProcessorValueTreeState& apvtsRef;

    KnobWithLabels targetLevelKnob;
    KnobWithLabels responseTimeKnob;
    CustomToggleButton enabledToggle;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (AgcControlsComponent)
};
