#include "GateControlsComponent.h"

GateControlsComponent::GateControlsComponent(juce::AudioProcessorValueTreeState& apvts)
    : apvtsRef(apvts),
      thresholdKnob(apvts, "gateThreshold", "THRESHOLD",
                    [](float v) { return juce::String(v, 1) + " dB"; }),
      attackKnob(apvts, "gateAttack", "ATTACK",
                 [](float v) { return juce::String(v, 0) + " ms"; }),
      holdKnob(apvts, "gateHold", "HOLD",
               [](float v) { return juce::String(v, 0) + " ms"; }),
      releaseKnob(apvts, "gateRelease", "RELEASE",
                  [](float v) { return juce::String(v, 0) + " ms"; }),
      enabledToggle(apvts, "gateEnabled", "ENABLED")
{
    addAndMakeVisible(thresholdKnob);
    addAndMakeVisible(attackKnob);
    addAndMakeVisible(holdKnob);
    addAndMakeVisible(releaseKnob);
    addAndMakeVisible(enabledToggle);
}

GateControlsComponent::~GateControlsComponent()
{
}

void GateControlsComponent::resized()
{
    auto bounds = getLocalBounds();
    int margin = 10;
    bounds.reduce(margin, margin);

    juce::Array<juce::Component*> firstRowItems = { &thresholdKnob, &attackKnob, &holdKnob };
    // For the second row, we have 1 knob and 1 toggle.
    // We can use a FlexBox for better alignment or a Grid with specific column spans.
    // Using FlexBox for simplicity here for the second row items.

    int numRows = 2; // Adjusted for Gate layout
    auto rowHeight = bounds.getHeight() / numRows;

    juce::Rectangle<int> rowBounds;

    // First row (3 knobs)
    rowBounds = bounds.removeFromTop(rowHeight);
    juce::Grid grid1;
    grid1.templateRows    = { juce::Grid::TrackInfo(juce::Grid::Fr(1)) };
    grid1.templateColumns = { juce::Grid::TrackInfo(juce::Grid::Fr(1)),
                              juce::Grid::TrackInfo(juce::Grid::Fr(1)),
                              juce::Grid::TrackInfo(juce::Grid::Fr(1)) };
    for (auto* item : firstRowItems) grid1.items.add(juce::GridItem(*item));
    grid1.performLayout(rowBounds.reduced(0, margin / 2));

    // Second row (1 knob, 1 toggle)
    rowBounds = bounds.removeFromTop(rowHeight);
    juce::FlexBox flex2;
    flex2.justifyContent = juce::FlexBox::JustifyContent::spaceAround; // Or center / flexStart
    flex2.alignItems = juce::FlexBox::AlignItems::center;
    // Add items with some flex properties to control their size if needed
    flex2.items.add(juce::FlexItem(releaseKnob).withFlex(1.0f).withMaxHeight(rowBounds.getHeight() * 0.9f));
    flex2.items.add(juce::FlexItem(enabledToggle).withFlex(1.0f).withMaxHeight(rowBounds.getHeight() * 0.9f));
    flex2.performLayout(rowBounds.reduced(0, margin/2));
}
