#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <functional>

class TabbedModuleSelectorComponent : public juce::Component
{
public:
    TabbedModuleSelectorComponent();
    ~TabbedModuleSelectorComponent() override;

    void paint(juce::Graphics& g) override;
    void resized() override;
    void mouseDown(const juce::MouseEvent& event) override;

    // Callback for when a tab is selected
    std::function<void(int)> onTabSelected;

    void setActiveTab(int tabIndex);
    int getActiveTab() const { return activeTabIndex; }

private:
    struct Tab
    {
        juce::String name;
        juce::Rectangle<int> bounds;
        bool isActive = false;
    };

    juce::Array<Tab> tabs;
    int activeTabIndex = 0;

    void updateTabBounds();

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (TabbedModuleSelectorComponent)
};
