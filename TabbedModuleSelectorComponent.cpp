#include "TabbedModuleSelectorComponent.h"
#include "CustomLookAndFeel.h"

TabbedModuleSelectorComponent::TabbedModuleSelectorComponent()
{
    // Initialize tabs
    tabs.add({"AGC", {}, false});
    tabs.add({"GATE", {}, false});
    tabs.add({"COMP", {}, true}); // Start with compressor active
    tabs.add({"DE-ESS", {}, false});
    tabs.add({"LIMIT", {}, false});
    
    activeTabIndex = 2; // Compressor
}

TabbedModuleSelectorComponent::~TabbedModuleSelectorComponent()
{
}

void TabbedModuleSelectorComponent::paint(juce::Graphics& g)
{
    auto bounds = getLocalBounds();
    
    // Draw glassmorphism background
    if (auto* customLookAndFeel = dynamic_cast<CustomLookAndFeel*>(&getLookAndFeel()))
    {
        customLookAndFeel->drawGlassmorphismPanel(g, bounds, juce::Colour(0xff0284c7));
    }
    
    // Draw tabs
    for (int i = 0; i < tabs.size(); ++i)
    {
        const auto& tab = tabs.getReference(i);
        auto tabBounds = tab.bounds.toFloat();
        
        if (tab.isActive)
        {
            // Active tab styling
            juce::ColourGradient activeGradient(
                CustomLookAndFeel::accentCyan, tabBounds.getTopLeft(),
                CustomLookAndFeel::accentBlue, tabBounds.getBottomRight(),
                false
            );
            g.setGradientFill(activeGradient);
            g.fillRoundedRectangle(tabBounds, 8.0f);
            
            // Active tab border
            g.setColour(CustomLookAndFeel::accentCyan.brighter(0.3f));
            g.drawRoundedRectangle(tabBounds, 8.0f, 2.0f);
            
            // Active tab text
            g.setColour(juce::Colours::white);
        }
        else
        {
            // Inactive tab styling
            g.setColour(CustomLookAndFeel::surfaceGlass.withAlpha(0.5f));
            g.fillRoundedRectangle(tabBounds, 8.0f);
            
            // Inactive tab border
            g.setColour(CustomLookAndFeel::borderColor.withAlpha(0.6f));
            g.drawRoundedRectangle(tabBounds, 8.0f, 1.0f);
            
            // Inactive tab text
            g.setColour(CustomLookAndFeel::textSecondary);
        }
        
        // Draw tab text
        g.setFont(juce::Font(12.0f, juce::Font::bold));
        g.drawText(tab.name, tabBounds.toNearestInt(), juce::Justification::centred);
    }
}

void TabbedModuleSelectorComponent::resized()
{
    updateTabBounds();
}

void TabbedModuleSelectorComponent::mouseDown(const juce::MouseEvent& event)
{
    auto clickPoint = event.getPosition();
    
    for (int i = 0; i < tabs.size(); ++i)
    {
        if (tabs.getReference(i).bounds.contains(clickPoint))
        {
            setActiveTab(i);
            if (onTabSelected)
                onTabSelected(i);
            break;
        }
    }
}

void TabbedModuleSelectorComponent::setActiveTab(int tabIndex)
{
    if (tabIndex >= 0 && tabIndex < tabs.size())
    {
        // Deactivate all tabs
        for (auto& tab : tabs)
            tab.isActive = false;
        
        // Activate selected tab
        tabs.getReference(tabIndex).isActive = true;
        activeTabIndex = tabIndex;
        
        repaint();
    }
}

void TabbedModuleSelectorComponent::updateTabBounds()
{
    auto bounds = getLocalBounds().reduced(12, 8); // Padding inside glassmorphism panel
    
    int tabWidth = bounds.getWidth() / tabs.size();
    int spacing = 4;
    int actualTabWidth = tabWidth - spacing;
    
    for (int i = 0; i < tabs.size(); ++i)
    {
        int x = bounds.getX() + (i * tabWidth) + (spacing / 2);
        tabs.getReference(i).bounds = juce::Rectangle<int>(x, bounds.getY(), actualTabWidth, bounds.getHeight());
    }
}
