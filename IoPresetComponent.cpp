#include "IoPresetComponent.h"
#include "PluginProcessor.h" // Ensure full definition for processorRef usage
#include "CustomLookAndFeel.h"

// Static array for preset info texts
static const std::map<juce::String, juce::String> PRESET_INFO_TEXTS = {
    {"default", "Optimal voice settings applied."},
    {"podcast", "Settings optimized for podcast production."},
    {"vocal_enhancer", "Enhances vocal clarity and presence."},
    {"noise_reduction", "Reduces background noise effectively."},
    {"custom", "Your custom preset settings."}
    // Add other presets as defined in CompliAudioProcessor::presets
};


IoPresetComponent::IoPresetComponent(CompliAudioProcessor& proc) : processorRef(proc)
{
    // Initialize preset info texts (can be done from a static map or similar)
    // This is just an example, align with your actual preset keys/names
    presetInfoTexts["Default"] = "Optimal voice settings applied."; // Assuming "Default" is a preset name
    presetInfoTexts["Podcast"] = "Settings optimized for podcast production.";
    presetInfoTexts["Vocal Enhancer"] = "Enhances vocal clarity and presence.";
    presetInfoTexts["Noise Reduction"] = "Reduces background noise effectively.";
    // ... add all preset info texts

    juce::Colour labelColour = juce::Colour(0xFFcbd5e1); // slate-300
    juce::Font labelFont (12.0f, juce::Font::plain);
    juce::Font infoFont (10.0f, juce::Font::italic);

    inputDeviceLabel.setText("INPUT DEVICE", juce::dontSendNotification);
    inputDeviceLabel.setFont(labelFont);
    inputDeviceLabel.setColour(juce::Label::textColourId, labelColour);
    addAndMakeVisible(inputDeviceLabel);

    outputDeviceLabel.setText("OUTPUT DEVICE", juce::dontSendNotification);
    outputDeviceLabel.setFont(labelFont);
    outputDeviceLabel.setColour(juce::Label::textColourId, labelColour);
    addAndMakeVisible(outputDeviceLabel);

    presetLabel.setText("PRESET", juce::dontSendNotification);
    presetLabel.setFont(labelFont);
    presetLabel.setColour(juce::Label::textColourId, labelColour);
    addAndMakeVisible(presetLabel);

    presetInfoLabel.setFont(infoFont);
    presetInfoLabel.setColour(juce::Label::textColourId, juce::Colour(0xFF06b6d4)); // cyan-400
    presetInfoLabel.setJustificationType(juce::Justification::topLeft);
    // presetInfoLabel.setWordWrap(juce::Label::WordWrap::wordWrap); // Not available in this JUCE version
    addAndMakeVisible(presetInfoLabel);

    addAndMakeVisible(inputDeviceComboBox);
    inputDeviceComboBox.addListener(this);

    addAndMakeVisible(outputDeviceComboBox);
    outputDeviceComboBox.addListener(this);

    addAndMakeVisible(presetSelectorComboBox);
    presetSelectorComboBox.addListener(this);
    // Populate presetSelectorComboBox
    juce::StringArray presetNames = {"Voice Optimized", "Broadcast", "Podcast", "Gaming", "Custom"};
    for (int i = 0; i < presetNames.size(); ++i)
    {
        presetSelectorComboBox.addItem(presetNames[i], i + 1);
    }
    // Create APVTS attachment
    // Ensure "preset" parameter ID matches one in APVTS
    if (auto* choiceParam = dynamic_cast<juce::AudioParameterChoice*>(processorRef.apvts.getParameter("preset")))
    {
         presetAttachment = std::make_unique<juce::AudioProcessorValueTreeState::ComboBoxAttachment>(processorRef.apvts, "preset", presetSelectorComboBox);
    }


    refreshInputButton.setButtonText("R"); // Placeholder, will be styled
    refreshInputButton.addListener(this);
    addAndMakeVisible(refreshInputButton);

    refreshOutputButton.setButtonText("R"); // Placeholder, will be styled
    refreshOutputButton.addListener(this);
    addAndMakeVisible(refreshOutputButton);

    populateInputDeviceList();
    populateOutputDeviceList();
    updatePresetInfoLabel(); // Initial update

    // Listen for audio device changes
    processorRef.getAudioDeviceManager().addChangeListener(this);
}

IoPresetComponent::~IoPresetComponent()
{
    // Remove audio device change listener
    processorRef.getAudioDeviceManager().removeChangeListener(this);
    // Attachments are auto-deleted. Listeners are auto-removed if this component is deleted.
}

void IoPresetComponent::paint (juce::Graphics& g)
{
    auto bounds = getLocalBounds();

    // Split into two sections
    auto leftSection = bounds.removeFromLeft(bounds.getWidth() / 2);
    auto rightSection = bounds;

    // Draw glassmorphism panels for each section
    if (auto* customLookAndFeel = dynamic_cast<CustomLookAndFeel*>(&getLookAndFeel()))
    {
        customLookAndFeel->drawGlassmorphismPanel(g, leftSection.reduced(8), juce::Colour(0xff06b6d4));
        customLookAndFeel->drawGlassmorphismPanel(g, rightSection.reduced(8), juce::Colour(0xff0891b2));
    }
}

void IoPresetComponent::resized()
{
    juce::FlexBox fb;
    fb.flexDirection = juce::FlexBox::Direction::row;
    fb.flexWrap = juce::FlexBox::Wrap::noWrap;
    fb.justifyContent = juce::FlexBox::JustifyContent::spaceBetween;

    int margin = 10;
    int labelHeight = 20;
    int comboBoxHeight = 25;
    int buttonSize = 25; // Square buttons for refresh
    int infoLabelHeight = 30;
    int sectionTitleY = margin + 5; // Y for section accent lines drawn in paint()

    // --- Left Column (Audio I/O) ---
    juce::FlexBox ioColumn;
    ioColumn.flexDirection = juce::FlexBox::Direction::column;

    ioColumn.items.add(juce::FlexItem(inputDeviceLabel).withHeight(labelHeight).withMargin(juce::FlexItem::Margin(0, 0, margin / 2, 0)));
    juce::FlexBox inputRow; // For ComboBox and Refresh Button
    inputRow.flexDirection = juce::FlexBox::Direction::row;
    inputRow.alignItems = juce::FlexBox::AlignItems::center;
    inputRow.items.add(juce::FlexItem(inputDeviceComboBox).withFlex(1.0f).withHeight(comboBoxHeight));
    inputRow.items.add(juce::FlexItem(refreshInputButton).withWidth(buttonSize).withHeight(buttonSize).withMargin(juce::FlexItem::Margin(0,0,0,margin/2)));
    ioColumn.items.add(juce::FlexItem(inputRow).withHeight(comboBoxHeight).withMargin(juce::FlexItem::Margin(0, 0, margin, 0)));

    ioColumn.items.add(juce::FlexItem(outputDeviceLabel).withHeight(labelHeight).withMargin(juce::FlexItem::Margin(margin, 0, margin / 2, 0)));
    juce::FlexBox outputRow;
    outputRow.flexDirection = juce::FlexBox::Direction::row;
    outputRow.alignItems = juce::FlexBox::AlignItems::center;
    outputRow.items.add(juce::FlexItem(outputDeviceComboBox).withFlex(1.0f).withHeight(comboBoxHeight));
    outputRow.items.add(juce::FlexItem(refreshOutputButton).withWidth(buttonSize).withHeight(buttonSize).withMargin(juce::FlexItem::Margin(0,0,0,margin/2)));
    ioColumn.items.add(juce::FlexItem(outputRow).withHeight(comboBoxHeight));

    // --- Right Column (Presets) ---
    juce::FlexBox presetColumn;
    presetColumn.flexDirection = juce::FlexBox::Direction::column;

    presetColumn.items.add(juce::FlexItem(presetLabel).withHeight(labelHeight).withMargin(juce::FlexItem::Margin(0, 0, margin / 2, 0)));
    presetColumn.items.add(juce::FlexItem(presetSelectorComboBox).withHeight(comboBoxHeight).withMargin(juce::FlexItem::Margin(0, 0, margin / 2, 0)));
    presetColumn.items.add(juce::FlexItem(presetInfoLabel).withHeight(infoLabelHeight).withFlex(1.0f)); // Allow to take remaining space

    // Add columns to main FlexBox
    fb.items.add(juce::FlexItem(ioColumn).withFlex(1.0f).withMargin(juce::FlexItem::Margin(margin, margin, margin, margin)));
    // Add a small spacer or rely on justifyContent
    fb.items.add(juce::FlexItem().withWidth(margin*2)); // Spacer
    fb.items.add(juce::FlexItem(presetColumn).withFlex(1.0f).withMargin(juce::FlexItem::Margin(margin, margin, margin, margin)));

    fb.performLayout(getLocalBounds().reduced(margin));

    // Manually set Y for accent lines based on label positions after FlexBox layout for paint()
    // This is a bit of a hack due to paint order. Better to make accent lines separate components or draw them relative to fixed points.
    // For now, we'll assume labels are positioned by FlexBox and paint() will use their Y.
    // The X positions for paint also need to be robust.
    // For instance, presetLabel.getX() might be 0 within its flex item, not global.
    // A simpler way for paint():
    // float ioSectionWidth = getWidth() / 2.0f - margin / 2.0f;
    // float presetSectionX = getWidth() / 2.0f + margin / 2.0f;
    // Then use these in paint() for the accent lines.
}


void IoPresetComponent::comboBoxChanged(juce::ComboBox* comboBoxThatHasChanged)
{
    if (comboBoxThatHasChanged == &inputDeviceComboBox)
    {
        juce::String selectedDevice = inputDeviceComboBox.getText();

        // Handle "None" selection
        if (selectedDevice == "(No Input Device)")
        {
            processorRef.setInputDevice("");
            juce::Logger::writeToLog("Input device disabled");
        }
        else if (selectedDevice.isNotEmpty())
        {
            processorRef.setInputDevice(selectedDevice);
            juce::Logger::writeToLog("Input device changed to: " + selectedDevice);
        }
    }
    else if (comboBoxThatHasChanged == &outputDeviceComboBox)
    {
        juce::String selectedDevice = outputDeviceComboBox.getText();

        // Handle "None" selection
        if (selectedDevice == "(No Output Device)")
        {
            processorRef.setOutputDevice("");
            juce::Logger::writeToLog("Output device disabled");
        }
        else if (selectedDevice.isNotEmpty())
        {
            processorRef.setOutputDevice(selectedDevice);
            juce::Logger::writeToLog("Output device changed to: " + selectedDevice);
        }
    }
    else if (comboBoxThatHasChanged == &presetSelectorComboBox)
    {
        updatePresetInfoLabel();
        // APVTS attachment handles parameter update
    }
}

void IoPresetComponent::buttonClicked(juce::Button* button)
{
    if (button == &refreshInputButton)
    {
        populateInputDeviceList();
    }
    else if (button == &refreshOutputButton)
    {
        populateOutputDeviceList();
    }
}

void IoPresetComponent::populateInputDeviceList()
{
    inputDeviceComboBox.clear();
    auto availableDevices = processorRef.getAvailableInputDevices();

    // Add "None" option for no input device
    inputDeviceComboBox.addItem("(No Input Device)", 1);

    int id = 2;
    for (const auto& deviceName : availableDevices)
    {
        inputDeviceComboBox.addItem(deviceName, id++);
    }

    // Select current device
    juce::String currentDevice = processorRef.getCurrentInputDevice();
    if (currentDevice.isEmpty())
    {
        inputDeviceComboBox.setSelectedId(1); // Select "None"
    }
    else
    {
        inputDeviceComboBox.setText(currentDevice, juce::dontSendNotification);
    }
}

void IoPresetComponent::populateOutputDeviceList()
{
    outputDeviceComboBox.clear();
    auto availableDevices = processorRef.getAvailableOutputDevices();

    // Add "None" option for no output device
    outputDeviceComboBox.addItem("(No Output Device)", 1);

    int id = 2;
    for (const auto& deviceName : availableDevices)
    {
        outputDeviceComboBox.addItem(deviceName, id++);
    }

    // Select current device
    juce::String currentDevice = processorRef.getCurrentOutputDevice();
    if (currentDevice.isEmpty())
    {
        outputDeviceComboBox.setSelectedId(1); // Select "None"
    }
    else
    {
        outputDeviceComboBox.setText(currentDevice, juce::dontSendNotification);
    }
}

void IoPresetComponent::updatePresetInfoLabel()
{
    juce::String selectedPresetName = presetSelectorComboBox.getText();
    // Find info text from the map (or PRESET_INFO_TEXTS if that's populated differently)
    auto it = presetInfoTexts.find(selectedPresetName);
    if (it != presetInfoTexts.end())
    {
        presetInfoLabel.setText(it->second, juce::dontSendNotification);
    }
    else
    {
        // Fallback if preset name not in map (e.g., "custom" or new presets)
        // Check against the static map too
        auto static_it = PRESET_INFO_TEXTS.find(selectedPresetName.toLowerCase()); // Assuming keys in PRESET_INFO_TEXTS are lowercase
        if (static_it != PRESET_INFO_TEXTS.end()) {
            presetInfoLabel.setText(static_it->second, juce::dontSendNotification);
        } else if (selectedPresetName.isNotEmpty()) {
             presetInfoLabel.setText("Custom settings active.", juce::dontSendNotification);
        } else {
            presetInfoLabel.setText("Select a preset to see info.", juce::dontSendNotification);
        }
    }
}

void IoPresetComponent::changeListenerCallback(juce::ChangeBroadcaster* source)
{
    // Check if the change came from the audio device manager
    if (source == &processorRef.getAudioDeviceManager())
    {
        // Update device lists when audio devices change
        juce::MessageManager::callAsync([this]()
        {
            populateInputDeviceList();
            populateOutputDeviceList();
        });
    }
}
