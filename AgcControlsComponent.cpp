#include "AgcControlsComponent.h"

AgcControlsComponent::AgcControlsComponent(juce::AudioProcessorValueTreeState& apvts)
    : apvtsRef(apvts),
      targetLevelKnob(apvts, "agcTargetLevel", "TARGET LVL",
                      [](float v) { return juce::String(v, 1) + " dB"; }),
      responseTimeKnob(apvts, "agcResponseTime", "RESPONSE",
                       [](float v) { return juce::String(v, 0) + " ms"; }),
      enabledToggle(apvts, "agcEnabled", "ENABLED")
{
    addAndMakeVisible(targetLevelKnob);
    addAndMakeVisible(responseTimeKnob);
    addAndMakeVisible(enabledToggle);
}

AgcControlsComponent::~AgcControlsComponent()
{
}

void AgcControlsComponent::resized()
{
    auto bounds = getLocalBounds();
    int margin = 10;
    bounds.reduce(margin, margin);

    juce::Array<juce::Component*> firstRowItems = { &targetLevelKnob, &responseTimeKnob };

    int numRows = 2;
    auto rowHeight = bounds.getHeight() / numRows;

    juce::Rectangle<int> rowBounds;

    // First row (2 knobs)
    rowBounds = bounds.removeFromTop(rowHeight);
    juce::Grid grid1;
    grid1.templateRows    = { juce::Grid::TrackInfo(juce::Grid::Fr(1)) };
    grid1.templateColumns = { juce::Grid::TrackInfo(juce::Grid::Fr(1)),
                              juce::Grid::TrackInfo(juce::Grid::Fr(1)) };
    for (auto* item : firstRowItems) grid1.items.add(juce::GridItem(*item));
    grid1.performLayout(rowBounds.reduced(0, margin / 2));

    // Second row (1 toggle, centered)
    rowBounds = bounds.removeFromTop(rowHeight);
    juce::FlexBox flex2;
    flex2.justifyContent = juce::FlexBox::JustifyContent::center; // Center the toggle
    flex2.alignItems = juce::FlexBox::AlignItems::center;
    // Give the toggle a reasonable size, e.g., by setting a flex basis or max width
    flex2.items.add(juce::FlexItem(enabledToggle).withFlex(0).withMinWidth(100).withMaxHeight(rowBounds.getHeight() * 0.5f)); // Example sizing
    flex2.performLayout(rowBounds.reduced(0, margin/2));
}
