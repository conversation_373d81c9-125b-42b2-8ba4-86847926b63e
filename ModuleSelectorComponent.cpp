#include "ModuleSelectorComponent.h"
#include "CustomLookAndFeel.h"

ModuleSelectorComponent::ModuleSelectorComponent()
{
    // Module Label
    moduleLabel.setText("MODULE", juce::dontSendNotification);
    moduleLabel.setFont(juce::Font (12.0f, juce::Font::bold));
    moduleLabel.setColour(juce::Label::textColourId, juce::Colour(0xFF38BDF8)); // sky-400
    moduleLabel.setJustificationType(juce::Justification::centredLeft);
    addAndMakeVisible(moduleLabel);

    // Module Selector ComboBox
    moduleSelectorComboBox.addItem("Compressor", 1);
    moduleSelectorComboBox.addItem("Noise Gate", 2);
    moduleSelectorComboBox.addItem("AGC", 3);
    moduleSelectorComboBox.addItem("De-esser", 4);
    moduleSelectorComboBox.addItem("Limiter", 5);

    moduleSelectorComboBox.setSelectedId(1, juce::dontSendNotification); // Default to Compressor
    moduleSelectorComboBox.addListener(this);
    addAndMakeVisible(moduleSelectorComboBox);
}

ModuleSelectorComponent::~ModuleSelectorComponent()
{
}

void ModuleSelectorComponent::paint (juce::Graphics& g)
{
    auto bounds = getLocalBounds();

    // Draw glassmorphism panel
    if (auto* customLookAndFeel = dynamic_cast<CustomLookAndFeel*>(&getLookAndFeel()))
    {
        customLookAndFeel->drawGlassmorphismPanel(g, bounds, juce::Colour(0xff0284c7));
    }
}

void ModuleSelectorComponent::resized()
{
    auto bounds = getLocalBounds();
    bounds.reduce(16, 12); // Padding inside the glassmorphism panel

    int labelWidth = 80;
    int spacing = 16;

    // Horizontal layout
    juce::FlexBox fb;
    fb.flexDirection = juce::FlexBox::Direction::row;
    fb.alignItems = juce::FlexBox::AlignItems::center;
    fb.justifyContent = juce::FlexBox::JustifyContent::flexStart;

    fb.items.add(juce::FlexItem(moduleLabel)
                 .withWidth(labelWidth)
                 .withMargin(juce::FlexItem::Margin(0, spacing, 0, 0)));

    fb.items.add(juce::FlexItem(moduleSelectorComboBox)
                 .withFlex(1.0f)
                 .withHeight(32));

    fb.performLayout(bounds);
}

void ModuleSelectorComponent::comboBoxChanged(juce::ComboBox* comboBoxThatHasChanged)
{
    // This component itself doesn't need to react directly to its ComboBox changes
    // if the PluginEditor is listening and handling the logic.
    // If it did, logic would go here.
    // For example, it could invoke a std::function callback.
    if (comboBoxThatHasChanged == &moduleSelectorComboBox)
    {
        // juce::Logger::writeToLog("ModuleSelectorComponent: Selection changed to " + moduleSelectorComboBox.getText());
    }
}
