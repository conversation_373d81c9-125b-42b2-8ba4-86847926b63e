#include "KnobWithLabels.h"

KnobWithLabels::KnobWithLabels(juce::AudioProcessorValueTreeState& state,
                               const juce::String& parameterID,
                               const juce::String& labelText,
                               std::function<juce::String(float)> formatter)
    : apvts(state),
      paramID(parameterID),
      valueToString(formatter)
{
    // Name Label
    nameLabel.setText(labelText, juce::dontSendNotification);
    nameLabel.setFont(juce::Font(10.0f, juce::Font::plain)); // text-xs
    nameLabel.setColour(juce::Label::textColourId, juce::Colour(0xFF94a3b8)); // slate-400
    nameLabel.setJustificationType(juce::Justification::centred);
    addAndMakeVisible(nameLabel);

    // Knob
    knob.setSliderStyle(juce::Slider::RotaryVerticalDrag);
    knob.setTextBoxStyle(juce::Slider::NoTextBox, false, 0, 0);
    knob.setColour(juce::Slider::thumbColourId, juce::Colour(0xFF06b6d4)); // From L&F, but can be here too
    knob.addListener(this);
    addAndMakeVisible(knob);

    // Attachment (must be after knob is configured)
    attachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(apvts, paramID, knob);

    // Value Label
    valueLabel.setFont(juce::Font(10.0f, juce::Font::bold)); // text-xs, but bold
    valueLabel.setColour(juce::Label::textColourId, juce::Colour(0xFF06b6d4)); // cyan-400
    valueLabel.setJustificationType(juce::Justification::centred);
    addAndMakeVisible(valueLabel);

    updateValueLabel(); // Initial update
}

KnobWithLabels::~KnobWithLabels()
{
    // Attachment and listeners are handled automatically
}

void KnobWithLabels::resized()
{
    auto bounds = getLocalBounds();
    int labelHeight = 15;
    int valueLabelHeight = 15;
    int knobMargin = 5; // Margin around the knob

    nameLabel.setBounds(bounds.removeFromTop(labelHeight));
    valueLabel.setBounds(bounds.removeFromBottom(valueLabelHeight));
    knob.setBounds(bounds.reduced(knobMargin)); // Knob takes the remaining space, with margin
}

void KnobWithLabels::sliderValueChanged(juce::Slider* slider)
{
    if (slider == &knob)
    {
        updateValueLabel();
    }
}

void KnobWithLabels::updateValueLabel()
{
    float value = knob.getValue();
    if (valueToString)
    {
        valueLabel.setText(valueToString(value), juce::dontSendNotification);
    }
    else
    {
        // Default formatting: show one decimal place if not an integer, otherwise no decimal places
        if (std::abs(value - static_cast<int>(value)) < 0.001f) // Check if it's effectively an integer
            valueLabel.setText(juce::String(static_cast<int>(value)), juce::dontSendNotification);
        else
            valueLabel.setText(juce::String(value, 1), juce::dontSendNotification);
    }
}
