#include "LevelMonitoringComponent.h"
#include "CustomLookAndFeel.h"

LevelMonitoringComponent::LevelMonitoringComponent(CompliAudioProcessor& proc)
    : processorRef(proc),
      inputMeter(juce::Colour(0xFF10B981), juce::Colour(0xFFEAB308), juce::Colour(0xFFDC2626), false), // Green, Yellow, Red
      outputMeter(juce::Colour(0xFF06B6D4), juce::Colour(0xFF3B82F6), juce::Colour(0xFF8B5CF6), false), // <PERSON>an, Blue, Violet
      grMeter(juce::Colour(0xFFF59E0B), juce::Colour(0xFFEF4444), juce::Colour(0xFFEF4444), true)     // Amber, Red, Red (GR meter)
{
    // Initialize Labels (texts, fonts, colors)
    juce::Font labelFont(10.0f, juce::Font::bold);
    juce::Colour labelColour = juce::Colour(0xFF94a3b8); // slate-400

    inputLabel.setText("INPUT", juce::dontSendNotification);
    inputLabel.setFont(labelFont);
    inputLabel.setColour(juce::Label::textColourId, labelColour);
    inputLabel.setJustificationType(juce::Justification::centredTop);
    addAndMakeVisible(inputLabel);

    outputLabel.setText("OUTPUT", juce::dontSendNotification);
    outputLabel.setFont(labelFont);
    outputLabel.setColour(juce::Label::textColourId, labelColour);
    outputLabel.setJustificationType(juce::Justification::centredTop);
    addAndMakeVisible(outputLabel);

    grLabel.setText("GR", juce::dontSendNotification);
    grLabel.setFont(labelFont);
    grLabel.setColour(juce::Label::textColourId, labelColour);
    grLabel.setJustificationType(juce::Justification::centredTop);
    addAndMakeVisible(grLabel);

    // Value Labels
    juce::Font valueFont(9.0f, juce::Font::plain);
    juce::Colour valueColour = juce::Colour(0xFFcbd5e1); // slate-300

    inputValueLabel.setText("- dB", juce::dontSendNotification);
    inputValueLabel.setFont(valueFont);
    inputValueLabel.setColour(juce::Label::textColourId, valueColour);
    inputValueLabel.setJustificationType(juce::Justification::centredBottom);
    addAndMakeVisible(inputValueLabel);

    outputValueLabel.setText("- dB", juce::dontSendNotification);
    outputValueLabel.setFont(valueFont);
    outputValueLabel.setColour(juce::Label::textColourId, valueColour);
    outputValueLabel.setJustificationType(juce::Justification::centredBottom);
    addAndMakeVisible(outputValueLabel);

    grValueLabel.setText("- dB", juce::dontSendNotification);
    grValueLabel.setFont(valueFont);
    grValueLabel.setColour(juce::Label::textColourId, valueColour);
    grValueLabel.setJustificationType(juce::Justification::centredBottom);
    addAndMakeVisible(grValueLabel);

    // Add and make visible meters
    addAndMakeVisible(inputMeter);
    addAndMakeVisible(outputMeter);
    addAndMakeVisible(grMeter);

    startTimerHz(30); // Refresh rate for meters
}

LevelMonitoringComponent::~LevelMonitoringComponent()
{
    stopTimer();
}

void LevelMonitoringComponent::paint (juce::Graphics& g)
{
    auto bounds = getLocalBounds();

    // Draw glassmorphism panel
    if (auto* customLookAndFeel = dynamic_cast<CustomLookAndFeel*>(&getLookAndFeel()))
    {
        customLookAndFeel->drawGlassmorphismPanel(g, bounds, juce::Colour(0xff10b981));
    }

    // Title
    g.setColour(juce::Colour(0xff10b981));
    g.setFont(juce::Font(14.0f, juce::Font::bold));
    auto titleBounds = bounds.removeFromTop(30).reduced(16, 8);
    g.drawText("LEVEL MONITORING", titleBounds, juce::Justification::centred);
}

void LevelMonitoringComponent::resized()
{
    auto bounds = getLocalBounds();
    bounds.removeFromTop(30); // Account for title
    bounds.reduce(16, 12); // Padding

    int labelHeight = 18;
    int valueLabelHeight = 16;
    int meterHeight = 16;
    int spacing = 16;

    // Vertical layout with three meter sections
    auto inputSection = bounds.removeFromTop(labelHeight + meterHeight + valueLabelHeight + spacing);
    inputSection.removeFromBottom(spacing);

    auto outputSection = bounds.removeFromTop(labelHeight + meterHeight + valueLabelHeight + spacing);
    outputSection.removeFromBottom(spacing);

    auto grSection = bounds.removeFromTop(labelHeight + meterHeight + valueLabelHeight);

    // Layout each section
    layoutMeterSection(inputSection, inputLabel, inputMeter, inputValueLabel, labelHeight, meterHeight, valueLabelHeight);
    layoutMeterSection(outputSection, outputLabel, outputMeter, outputValueLabel, labelHeight, meterHeight, valueLabelHeight);
    layoutMeterSection(grSection, grLabel, grMeter, grValueLabel, labelHeight, meterHeight, valueLabelHeight);
}

void LevelMonitoringComponent::layoutMeterSection(juce::Rectangle<int> bounds,
                                                 juce::Label& label,
                                                 LevelMeterComponent& meter,
                                                 juce::Label& valueLabel,
                                                 int labelHeight, int meterHeight, int valueLabelHeight)
{
    label.setBounds(bounds.removeFromTop(labelHeight));
    valueLabel.setBounds(bounds.removeFromBottom(valueLabelHeight));
    meter.setBounds(bounds.reduced(0, 2)); // Small vertical padding for meter
}

void LevelMonitoringComponent::timerCallback()
{
    // Using the correct atomic member names from PluginProcessor
    inputMeter.setLevel(processorRef.inputLevel.load());
    outputMeter.setLevel(processorRef.outputLevel.load());
    grMeter.setLevel(processorRef.gainReduction.load());

    updateValueLabels();
}

void LevelMonitoringComponent::updateValueLabels()
{
    // Using the correct atomic member names from PluginProcessor
    inputValueLabel.setText(juce::String(processorRef.inputLevel.load(), 1) + " dB", juce::dontSendNotification);
    outputValueLabel.setText(juce::String(processorRef.outputLevel.load(), 1) + " dB", juce::dontSendNotification);
    grValueLabel.setText(juce::String(processorRef.gainReduction.load(), 1) + " dB", juce::dontSendNotification);
}
