#include "LevelMeterComponent.h"

LevelMeterComponent::LevelMeterComponent(const juce::Colour& startCol,
                                       const juce::Colour& midCol,
                                       const juce::Colour& endCol,
                                       bool isGR)
    : gradientStart(startCol), gradientMid(midCol), gradientEnd(endCol), isGainReductionMeter(isGR)
{
    // Initial level is already set by currentLevelDb member initializer
}

LevelMeterComponent::~LevelMeterComponent()
{
}

void LevelMeterComponent::setLevel(float newLevelDb)
{
    // Ensure newLevelDb is within a reasonable range if necessary,
    // though jlimit in paint will also handle this for drawing.
    currentLevelDb = newLevelDb;
    repaint();
}

void LevelMeterComponent::setIsGainReduction(bool isGR)
{
    isGainReductionMeter = isGR;
    repaint(); // In case visual representation changes
}

void LevelMeterComponent::paint (juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();
    float cornerRadius = 3.0f;

    // Background
    g.setColour(juce::Colour(0xFF374151)); // dark slate-700/800 equivalent
    g.fillRoundedRectangle(bounds, cornerRadius);

    // Determine proportion based on meter type
    float proportion = 0.0f;
    if (isGainReductionMeter)
    {
        // GR meter: 0dB (no reduction) is full height (or no bar), -20dB (max reduction) is 0 height (or full bar if inverted)
        // Assuming more reduction means the bar "grows" downwards or from right to left.
        // Let's say 0dB = 0 proportion, grMinDb (-24dB) = 1.0 proportion (full bar representing reduction)
        proportion = (juce::jlimit(grMinDb, grMaxDb, currentLevelDb) - grMaxDb) / (grMinDb - grMaxDb);
    }
    else
    {
        proportion = (juce::jlimit(minDb, maxDb, currentLevelDb) - minDb) / (maxDb - minDb);
    }

    proportion = juce::jlimit(0.0f, 1.0f, proportion); // Ensure it's strictly within 0-1

    if (proportion > 0.0f)
    {
        juce::Rectangle<float> meterBarBounds;
        if (isGainReductionMeter) {
            // GR meter fills from top down (if vertical) or right to left (if horizontal)
            // Assuming horizontal meter here, like typical plugin GR meters
             meterBarBounds = bounds.withLeft(bounds.getWidth() * (1.0f - proportion)).reduced(2.0f);
        } else {
            meterBarBounds = bounds.withWidth(bounds.getWidth() * proportion).reduced(2.0f); // Margin of 2px
        }


        juce::ColourGradient gradient(gradientStart, meterBarBounds.getBottomLeft(),
                                      gradientEnd, meterBarBounds.getTopRight(), // Diagonal gradient
                                      false);
        // Add mid color stop if applicable (for 3-color gradients)
        if (gradientMid != juce::Colours::transparentBlack) // Check if mid color is set
        {
            gradient.addColour(0.5, gradientMid);
        }
        g.setGradientFill(gradient);
        g.fillRoundedRectangle(meterBarBounds, cornerRadius);
    }
}
