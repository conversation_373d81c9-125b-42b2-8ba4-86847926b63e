# Ignore build artifacts and IDE files that are too large for git
.idea/
build/
cmake-build-debug/
*.obj
*.db
*.pdb
*.exe
*.lib
*.ilk
*.tlog
*.suo
*.bin
*.cache
*.log
*.user
*.sdf
*.ipch
*.VC.db
*.VC.opendb
*.recipe
*.vscode/
.vs/
JUCE/
# Ignore CMake and CLion files
CMakeLists.txt.user
CMakeCache.txt
CMakeFiles/
CMakeScripts/
Testing/
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/
CMakeUserPresets.json
# Ignore all artefacts and temp files
CompliAudioProcessor_artefacts/
# Ignore solution and project files generated by IDEs
*.vcxproj
*.vcxproj.filters
*.sln
# Ignore all files larger than 50MB (handled by git-lfs or manual exclusion)
# (Note: git cannot ignore already tracked files by size, but this is a reminder)
