#include "HeaderComponent.h"

HeaderComponent::HeaderComponent()
{
    // Title Label with gradient text effect
    titleLabel.setText("compli", juce::dontSendNotification);
    titleLabel.setFont(juce::Font (48.0f, juce::Font::bold)); // Larger, more prominent
    titleLabel.setJustificationType(juce::Justification::centred);
    titleLabel.setColour(juce::Label::textColourId, juce::Colour(0xFF06B6D4)); // Bright cyan
    addAndMakeVisible(titleLabel);

    // Subtitle Label with tracking
    subtitleLabel.setText("AUDIO PROCESSOR", juce::dontSendNotification);
    subtitleLabel.setFont(juce::Font (12.0f, juce::Font::bold)); // Slightly larger and bold
    subtitleLabel.setJustificationType(juce::Justification::centred);
    subtitleLabel.setColour(juce::Label::textColourId, juce::Colour(0xFF94A3B8)); // slate-400
    addAndMakeVisible(subtitleLabel);
}

HeaderComponent::~HeaderComponent()
{
}

void HeaderComponent::paint (juce::Graphics& g)
{
    auto bounds = getLocalBounds();

    // Background gradient overlay
    juce::ColourGradient bgGradient(
        juce::Colours::transparentBlack, bounds.getCentreX(), bounds.getY(),
        juce::Colour(0x1a06b6d4), bounds.getCentreX(), bounds.getBottom(), // cyan with low alpha
        false
    );
    g.setGradientFill(bgGradient);
    g.fillRoundedRectangle(bounds.toFloat(), 8.0f);

    // Decorative line below subtitle
    float lineWidth = 96.0f;
    float lineHeight = 4.0f;
    float lineX = (getWidth() - lineWidth) / 2.0f;
    float lineY = subtitleLabel.getBottom() + 8.0f;

    juce::ColourGradient lineGradient(juce::Colour(0xFF06B6D4), // cyan-500
                                     lineX, lineY,
                                     juce::Colour(0xFF3B82F6), // blue-500
                                     lineX + lineWidth, lineY,
                                     false); // Horizontal gradient

    g.setGradientFill(lineGradient);
    g.fillRoundedRectangle(lineX, lineY, lineWidth, lineHeight, lineHeight / 2.0f);
}

void HeaderComponent::resized()
{
    int verticalMargin = 10; // Top margin
    int spacing = 2;         // Spacing between title and subtitle
    int titleHeight = 40;
    int subtitleHeight = 15;

    titleLabel.setBounds(0, verticalMargin, getWidth(), titleHeight);
    subtitleLabel.setBounds(0, titleLabel.getBottom() + spacing, getWidth(), subtitleHeight);

    // The line's Y position depends on subtitleLabel.getBottom(), which is set here.
    // So, paint() will correctly calculate it.
    // If more complex layout dependencies, could store lineYPosition as a member.
}
