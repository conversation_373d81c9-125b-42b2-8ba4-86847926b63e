#pragma once

#include <juce_audio_processors/juce_audio_processors.h>
#include <juce_dsp/juce_dsp.h>
#include <juce_audio_devices/juce_audio_devices.h>

#if JucePlugin_Build_Standalone
#include <juce_audio_plugin_client/juce_audio_plugin_client.h>
#endif

//==============================================================================
// Custom DSP modules for comprehensive voice processing
class AGCProcessor
{
public:
    void prepare(const juce::dsp::ProcessSpec& spec);
    void reset();
    void setTargetLevel(float targetLevelDb);
    void setResponseTime(float responseTimeMs);
    void process(juce::dsp::AudioBlock<float>& block);
    float getCurrentGainReduction() const { return currentGainReduction; }

private:
    float targetLevel = -18.0f;
    float responseTime = 1000.0f;
    float currentGain = 1.0f;
    float currentGainReduction = 0.0f;
    double sampleRate = 44100.0;
    juce::dsp::BallisticsFilter<float> ballistics;
};

class DeEsserProcessor
{
public:
    void prepare(const juce::dsp::ProcessSpec& spec);
    void reset();
    void setFrequency(float frequencyHz);
    void setThreshold(float thresholdDb);
    void setReduction(float reductionDb);
    void process(juce::dsp::AudioBlock<float>& block);
    float getCurrentReduction() const { return currentReduction; }

private:
    float frequency = 6000.0f;
    float threshold = -12.0f;
    float reduction = 6.0f;
    float currentReduction = 0.0f;
    juce::dsp::IIR::Filter<float> highpassFilter;
    juce::dsp::IIR::Filter<float> lowpassFilter;
    juce::dsp::Compressor<float> sibilanceCompressor;
    double sampleRate = 44100.0;
};

class MultibandCompressor
{
public:
    void prepare(const juce::dsp::ProcessSpec& spec);
    void reset();
    void setEnabled(bool enabled);
    void setCrossoverFrequencies(float lowMid, float midHigh);
    void setBandParameters(int band, float threshold, float ratio, float attack, float release, float makeupGain);
    void process(juce::dsp::AudioBlock<float>& block);
    float getBandGainReduction(int band) const;

private:
    bool isEnabled = false;
    static constexpr int numBands = 3;

    juce::dsp::LinkwitzRileyFilter<float> lowMidCrossover;
    juce::dsp::LinkwitzRileyFilter<float> midHighCrossover;
    std::array<juce::dsp::Compressor<float>, numBands> bandCompressors;
    std::array<juce::dsp::Gain<float>, numBands> bandGains;
    std::array<float, numBands> bandGainReduction = {0.0f, 0.0f, 0.0f};
};

//==============================================================================
class CompliAudioProcessor final : public juce::AudioProcessor
{
public:
    //==============================================================================
    CompliAudioProcessor();
    ~CompliAudioProcessor() override;

    //==============================================================================
    void prepareToPlay (double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;

    bool isBusesLayoutSupported (const BusesLayout& layouts) const override;

    void processBlock (juce::AudioBuffer<float>&, juce::MidiBuffer&) override;
    using AudioProcessor::processBlock;

    //==============================================================================
    juce::AudioProcessorEditor* createEditor() override;
    bool hasEditor() const override;

    //==============================================================================
    const juce::String getName() const override;

    bool acceptsMidi() const override;
    bool producesMidi() const override;
    bool isMidiEffect() const override;
    double getTailLengthSeconds() const override;

    //==============================================================================
    int getNumPrograms() override;
    int getCurrentProgram() override;
    void setCurrentProgram (int index) override;
    const juce::String getProgramName (int index) override;
    void changeProgramName (int index, const juce::String& newName) override;

    //==============================================================================
    void getStateInformation (juce::MemoryBlock& destData) override;
    void setStateInformation (const void* data, int sizeInBytes) override;

    static juce::AudioProcessorValueTreeState::ParameterLayout createParameterLayout();
    juce::AudioProcessorValueTreeState apvts {*this, nullptr, "Parameters", createParameterLayout()};

    // Level meters and processing meters
    std::atomic<float> inputLevel{0.0f};
    std::atomic<float> outputLevel{0.0f};
    std::atomic<float> gainReduction{0.0f};
    std::atomic<float> agcGainReduction{0.0f};
    std::atomic<float> gateActivity{0.0f};
    std::atomic<float> deEsserReduction{0.0f};
    std::atomic<float> multibandGR1{0.0f};
    std::atomic<float> multibandGR2{0.0f};
    std::atomic<float> multibandGR3{0.0f};

    // Preset management
    void loadPreset(int presetIndex);
    void updatePresetParameters();

    // Audio device management (for standalone)
    juce::AudioDeviceManager& getAudioDeviceManager();
    juce::StringArray getAvailableInputDevices();
    juce::StringArray getAvailableOutputDevices();
    void setInputDevice(const juce::String& deviceName);
    void setOutputDevice(const juce::String& deviceName);
    juce::String getCurrentInputDevice();
    juce::String getCurrentOutputDevice();

private:
    //==============================================================================
    // Audio device management
    juce::AudioDeviceManager audioDeviceManager;

    // Comprehensive DSP processing chain
    // Order: AGC → Noise Gate → Compressor → De-esser → Limiter
    AGCProcessor agcProcessor;
    juce::dsp::NoiseGate<float> noiseGate;
    juce::dsp::Compressor<float> compressor;
    MultibandCompressor multibandCompressor;
    DeEsserProcessor deEsserProcessor;
    juce::dsp::Limiter<float> limiter;

    // Enhanced preset definitions
    struct PresetData
    {
        // AGC parameters
        float agcTargetLevel, agcResponseTime;
        bool agcEnabled;

        // Noise Gate parameters
        float gateThreshold, gateAttack, gateHold, gateRelease;
        bool gateEnabled;

        // Compressor parameters
        float threshold, ratio, attack, release, makeupGain;
        bool multibandEnabled;

        // Multiband crossover frequencies
        float lowMidCrossover, midHighCrossover;

        // De-esser parameters
        float deEsserFreq, deEsserThreshold, deEsserReduction;
        bool deEsserEnabled;

        // Limiter parameters
        float limiterThreshold, limiterAttack, limiterRelease;
        bool limiterEnabled;

        juce::String name;
    };

    static const std::array<PresetData, 5> presets;
    int currentPresetIndex = 0;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (CompliAudioProcessor)
};
