#pragma once

#include <juce_gui_basics/juce_gui_basics.h>
#include <juce_audio_processors/juce_audio_processors.h>
#include "KnobWithLabels.h"
#include "CustomToggleButton.h"

class DeesserControlsComponent : public juce::Component
{
public:
    DeesserControlsComponent(juce::AudioProcessorValueTreeState& apvts);
    ~DeesserControlsComponent() override;

    void resized() override;

private:
    juce::AudioProcessorValueTreeState& apvtsRef;

    KnobWithLabels frequencyKnob;
    KnobWithLabels thresholdKnob;
    KnobWithLabels reductionKnob; // Or should this be "Range" or "Strength"? "REDUCTION" for now.
    CustomToggleButton enabledToggle;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (DeesserControlsComponent)
};
