#pragma once

#include <juce_gui_basics/juce_gui_basics.h>

class LevelMeterComponent : public juce::Component
{
public:
    LevelMeterComponent(const juce::Colour& startCol,
                        const juce::Colour& midCol,
                        const juce::Colour& endCol,
                        bool isGR = false);
    ~LevelMeterComponent() override;

    void paint (juce::Graphics& g) override;
    void setLevel(float newLevel); // newLevel is expected to be in dB
    void setIsGainReduction(bool isGR);


private:
    float currentLevelDb = -60.0f; // Store dB value, initialize to a low value
    juce::Colour gradientStart, gradientMid, gradientEnd;
    bool isGainReductionMeter = false;

    // Define dB range for normalization
    const float minDb = -60.0f;
    const float maxDb = 6.0f; // Allow some headroom display
    const float grMaxDb = 0.0f; // For GR meter, 0dB is typically the top
    const float grMinDb = -24.0f; // GR meter might show down to -24dB reduction

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (LevelMeterComponent)
};
